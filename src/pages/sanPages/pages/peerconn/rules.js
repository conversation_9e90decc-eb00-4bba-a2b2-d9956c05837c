import u from 'lodash';

import {PeerConnStatus, PeerConnRole, DNSSyncStatus, PayType} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import {isCrossRegion} from '@/pages/sanPages/utils/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import moment from 'moment';

function payPayRelease(item) {
    return (
        item.productType === PayType.PREPAY &&
        item.status !== PeerConnStatus.CONSULT_FAILED &&
        item.status !== PeerConnStatus.EXPIRED &&
        item.status !== PeerConnStatus.AUDIT_FAILED
    );
}
function postpayRelease(item) {
    return item.status === PeerConnStatus.CONSULTING;
}

const domains = window.$context.getDomains();
const AllRegion = window.$context.getEnum('AllRegion');
export default {
    createPeer: [
        {
            required: false,
            message: ''
        },
        {
            custom(data) {
                if (window.$context.getCurrentRegionId === AllRegion.HK02) {
                    return {
                        disable: true,
                        message: `该地域暂不支持该功能。如需使用该功能，请提交
                            <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>
                        `
                    };
                }
            }
        }
    ],
    applyPeer: [
        {
            required: false,
            message: ''
        },
        {
            custom(data, options) {
                if (!options.data || !options.data.result || options.data.result.length === 0) {
                    return {
                        disable: true,
                        message: '暂无申请'
                    };
                }
            }
        }
    ],
    recharge: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行续费'
        },
        {
            status: [PeerConnStatus.ACTIVE, PeerConnStatus.EXPIRED],
            message: '该状态下的对等连接不支持续费。如需操作，请稍后重试'
        },
        {
            productType: ['prepay'],
            field: 'productType',
            message: '后付费实例无需进行续费操作'
        },
        {
            custom(data) {
                if (u.some(data, item => item.orderStatus)) {
                    return {
                        disable: true,
                        message: '计费变更中的对等连接不支持续费。如需操作，请稍后重试'
                    };
                }
                if (u.some(data, item => item.role !== PeerConnRole.INITIATOR)) {
                    return {
                        disable: true,
                        message: '非发起端的对等连接不支持续费。如需操作，请发起端进行续费'
                    };
                }
                if (u.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                      ${
                          FLAG.NetworkSupportXS
                              ? '订单管理'
                              : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                      }页面处理`
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            custom(data) {
                if (u.some(data, item => item.status === PeerConnStatus.AUDIT_FAILED)) {
                    return {
                        disable: true,
                        message: '该实例当前状态不可进行该操作'
                    };
                }
            }
        }
    ],
    UPGRADE: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            status: [PeerConnStatus.ACTIVE],
            message: '仅可用状态的对等连接可进行该操作'
        },
        {
            custom(data) {
                const isExistPrepay = u.find(data, item => item.productType === PayType.PREPAY);
                const oneDayTimeStamp = 24 * 60 * 60 * 1000;
                const isLastOneDay = u.find(
                    data,
                    item => moment(item.expireTime).valueOf() - moment().valueOf() < oneDayTimeStamp
                );
                if (isExistPrepay && isLastOneDay) {
                    return {
                        disable: true,
                        message: '到期前1天内无法操作，如需操作，请续费后操作。'
                    };
                }
                if (u.find(data, item => item.orderStatus)) {
                    return {
                        disable: true,
                        message: '计费变更中的对等连接不可进行该操作。如需操作，请稍后重试'
                    };
                }
                if (u.find(data, item => item.role !== PeerConnRole.INITIATOR)) {
                    return {
                        disable: true,
                        message: '仅发起端的对等连接可进行该操作'
                    };
                }
                if (u.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                              ${
                                  FLAG.NetworkSupportXS
                                      ? '订单管理'
                                      : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                              }页面处理`
                    };
                }
            }
        }
    ],
    RELEASE: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            orderAble: false,
            message: '该实例存在正在审核的订单。如需操作，请稍候重试'
        },
        {
            custom(data) {
                if (u.some(data, item => item.role !== PeerConnRole.INITIATOR)) {
                    return {
                        disable: true,
                        message: '仅发起端的对等连接可进行该操作'
                    };
                }
                if (u.some(data, item => item.status === 'auditing')) {
                    return {
                        disable: true,
                        message: '审核中的对等连接不可进行释放'
                    };
                }
                if (u.some(data, item => postpayRelease(item))) {
                    return {
                        disable: true,
                        message: '协商中的对等连接不可进行释放。待协商完毕后，可进行该操作'
                    };
                }
                if (u.some(data, item => payPayRelease(item))) {
                    return {
                        disable: true,
                        message: `预付费实例不可释放，如需释放，
                        可提<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>退订`
                    };
                }
                if (u.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                      ${
                          FLAG.NetworkSupportXS
                              ? '订单管理'
                              : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                      }页面处理`
                    };
                }
                if (u.find(data, item => item.deleteProtect)) {
                    return {
                        disable: true,
                        message: '该实例开启了释放保护功能，请在实例详情页面中关闭释放保护后再点击释放'
                    };
                }
                const {localRegion, peerRegion} = data[0];
                const crossRegion = [AllRegion.HK, AllRegion.HKG, AllRegion.HK02, AllRegion.SIN];
                if (
                    u.find(data, item => item.status === 'expired') &&
                    (crossRegion.includes(localRegion) || crossRegion.includes(peerRegion)) &&
                    localRegion !== peerRegion
                ) {
                    return {
                        disable: true,
                        message: '已过期的跨境对等连接不支持该操作'
                    };
                }
            }
        }
    ],
    ALTER_PRODUCTTYPE: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            status: [PeerConnStatus.ACTIVE],
            message: '非可用状态的对等连接暂不支持计费变更。'
        },
        {
            custom(data, options) {
                if (u.find(data, item => item.orderStatus)) {
                    return {
                        disable: true,
                        message: '计费变更中，如需再次计费变更，请稍后重试'
                    };
                }
                if (u.find(data, item => item.role !== PeerConnRole.INITIATOR)) {
                    return {
                        disable: true,
                        message: '连接端不可进行计费变更。如需操作，请发起端进行'
                    };
                }
                if (u.find(data, item => isCrossRegion(item.localRegion, item.peerRegion))) {
                    return {
                        disable: true,
                        message: '跨境对等连接暂不支持该操作'
                    };
                }
                if (u.find(data, item => item.task === 'auto_renew')) {
                    return {
                        disable: true,
                        message: `自动续费的对等连接不支持计费变更。如需操作，请先关闭计费变更，关闭计费请点击<a href="/billing/#/renew/list" target="_blank">续费</a>`
                    };
                }
                if (u.find(data, item => !item.productType)) {
                    return {
                        disable: true,
                        message: '当前状态的对等连接暂不支持计费变更'
                    };
                }
                if (u.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                      ${
                          FLAG.NetworkSupportXS
                              ? '订单管理'
                              : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                      }页面处理`
                    };
                }
            }
        }
    ],
    CANCEL_ALTER_PRODUCTTYPE: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            status: [PeerConnStatus.ACTIVE],
            message: '未进行计费变更或计费变更已生效的实例不能取消计费变更操作'
        },
        {
            custom(data) {
                if (u.some(data, item => item.role !== PeerConnRole.INITIATOR)) {
                    return {
                        disable: true,
                        message: '连接端不可进行计费变更。如需操作，请发起端进行'
                    };
                }
                if (u.some(data, item => item.orderStatus !== 'to_postpay' && item.orderStatus !== 'shift_charge')) {
                    return {
                        disable: true,
                        message: '未进行计费变更无法进行该操作'
                    };
                }
                if (u.some(data, item => item.orderStatus && item.orderStatus !== 'to_postpay')) {
                    return {
                        disable: true,
                        message: '后付费转预付费暂不支持该操作'
                    };
                }
            }
        }
    ],
    OPEN_DNS_SYNC: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            custom(data) {
                if (
                    u.some(
                        data,
                        item => item.status !== PeerConnStatus.ACTIVE || item.dnsStatus !== DNSSyncStatus.CLOSE
                    )
                ) {
                    return {
                        disable: true,
                        message: '当前状态无法开启DNS同步'
                    };
                }
            }
        }
    ],
    CLOSE_DNS_SYNC: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            custom(data) {
                if (
                    u.some(
                        data,
                        item =>
                            item.status !== PeerConnStatus.ACTIVE ||
                            (item.dnsStatus !== DNSSyncStatus.OPEN && item.dnsStatus !== DNSSyncStatus.WAIT)
                    )
                ) {
                    return {
                        disable: true,
                        message: '当前状态不支持关闭DNS同步'
                    };
                }
            }
        }
    ]
};
