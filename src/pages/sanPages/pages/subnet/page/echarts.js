import * as echarts from 'echarts/core';
import {TitleComponent, LegendComponent, TooltipComponent} from 'echarts/components';
import {<PERSON><PERSON><PERSON>} from 'echarts/charts';
import {LabelLayout, UniversalTransition} from 'echarts/features';
import {CanvasRenderer} from 'echarts/renderers';

echarts.use([
    Pie<PERSON><PERSON>,
    TitleComponent,
    LegendComponent,
    LabelLayout,
    UniversalTransition,
    CanvasRenderer,
    TooltipComponent
]);

const echartsColorMapLess = [
    '#2468F2',
    '#A5E693',
    '#FAD000',
    '#F33E3E',
    '#A985FF',
    '#005C99',
    '#87D26D',
    '#FF8E52',
    '#E62E6B',
    '#95B8FF'
];
// 初始化echart
var ipResourcePieEchart;
export function initIPResourcePieEcharts(chartDom, echartsData, usedIp, totalIpcount) {
    // 存在则销毁后初始化
    ipResourcePieEchart && ipResourcePieEchart.dispose();
    ipResourcePieEchart = echarts.init(chartDom);
    ipResourcePieEchart.resize({
        width: '560px',
        height: '320px'
    });
    let totalIP = totalIpcount ? totalIpcount : '-';
    let nameArr = [];
    let ipLength;
    let colorArr = [];
    if (echartsData && echartsData.length) {
        nameArr = echartsData.map(item => {
            return item.name;
        });
        ipLength = nameArr.length;
        colorArr = echartsColorMapLess;
    } else {
        nameArr = [''];
        echartsData = [{name: '', value: 0}];
        colorArr = ['#D8D8D8'];
    }
    let option = {
        // 图表属性
        legend: {
            orient: 'vertical',
            icon: 'circle',
            itemHeight: 8,
            itemWidth: 8,
            height: 600,
            width: 300,
            right: '0%',
            top: '5%',
            formatter: params => {
                let arr = [];
                for (let i = 0; i < echartsData.length; i++) {
                    let labelStyle = 'label';
                    if (
                        echartsData.some(item => item.value > 10000) &&
                        echartsData.some(item => item.name.length >= 6)
                    ) {
                        labelStyle = 'longTextlabel';
                    } else if (
                        (echartsData.some(item => item.value > 10000) &&
                            echartsData.some(item => item.name.length < 6)) ||
                        (echartsData.some(item => item.value <= 10000) &&
                            echartsData.some(item => item.name.length >= 6))
                    ) {
                        labelStyle = 'longValueLabel';
                    } else {
                        labelStyle = 'label';
                    }
                    let valueStyle = echartsData.some(item => item.value > 10000) ? 'longValue' : 'value';
                    if (echartsData[i].name === params) {
                        arr.push(
                            `{${labelStyle}|` +
                                echartsData[i].name +
                                '     }' +
                                `                   {${valueStyle}|` +
                                     echartsData[i].value +
                                '个}' +
                                '{percent|' +
                                echartsData[i].percentValue +
                                '%}'
                        );
                    }
                }
                return colorArr.length === 1 ? '{nonText|您还没有任何IP使用数据}' : arr.join('\n');
            },
            textStyle: {
                rich: {
                    longValueLabel: {
                        fontSize: 12,
                        color: '#303540',
                        lineHeight: 20,
                        fontWeight: 400,
                        width: 150,
                        padding: [6, -82, 6, 0]
                    },
                    longTextlabel: {
                        fontSize: 12,
                        color: '#303540',
                        lineHeight: 20,
                        fontWeight: 400,
                        width: 150,
                        overflow: 'break',
                        padding: [6, -4, 6, 0]
                    },
                    label: {
                        fontSize: 12,
                        color: '#303540',
                        lineHeight: 20,
                        fontWeight: 400,
                        width: 150,
                        padding: [6, -80, 6, 0]
                    },
                    longValue: {
                        fontSize: 14,
                        color: '#151B26',
                        fontWeight: 400,
                        width: 30,
                        padding: [6, 55, 6, 0]
                    },
                    value: {
                        fontSize: 14,
                        color: '#151B26',
                        fontWeight: 400,
                        width: 30,
                        padding: [6, 14, 6, 0]
                    },
                    percent: {
                        fontSize: 12,
                        color: '#84868C',
                        fontWeight: 400,
                        width: 20,
                        padding: [6, 0, 6, 0]
                    },
                    nonText: {
                        fontSize: 14,
                        color: '#B8BABF',
                        lineHeight: 22,
                        fontWeight: 400
                    }
                }
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: params => {
                let text =
                    colorArr.length === 1 ? '您还没有任何IP使用数据' : params.data.fullName + ' ' + params.value + '个';
                return text;
            }
        },
        series: [
            {
                type: 'pie',
                radius: ['60%', '80%'],
                center: ['30%', '50%'],
                right: '10%',
                data: echartsData,
                label: {
                    normal: {
                        show: true,
                        position: 'center',
                        formatter: `{text| IP使用数量/总数量 }\n\r{active|${usedIp}个/}{total|${totalIP}个}`,
                        rich: {
                            text: {
                                fontSize: 12,
                                color: '#B8BABF',
                                fontWeight: 400,
                                padding: [0, 0, 5, 0]
                            },
                            active: {
                                fontSize: 18,
                                color: '#151B26',
                                fontWeight: 400
                            },
                            total: {
                                fontSize: 18,
                                color: '#B8BABF',
                                fontWeight: 400
                            }
                        }
                    }
                },
                color: colorArr
            }
        ]
    };
    option && ipResourcePieEchart.setOption(option);
}
