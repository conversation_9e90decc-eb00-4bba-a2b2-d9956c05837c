import {DcGatewayStatus} from '../../common/enum';
import {ContextService} from '../../common';
const AllRegion = ContextService.getEnum('AllRegion');
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

export default {
    createDc: [
        {
            required: false
        },
        {
            custom(data, options = {}) {
                if (window.$context.getCurrentRegionId() === AllRegion.HK02) {
                    return {
                        disable: true,
                        message: `该地域暂不支持该功能。如需使用该功能，请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                    };
                }
            }
        }
    ],
    release: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        },
        {
            custom(data, options) {
                if (data && data[0] && ['deleted', 'error', 'switching'].includes(data[0].status)) {
                    return {
                        disable: true,
                        message: '当前状态不允许释放'
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            status: [DcGatewayStatus.RUNNING, DcGatewayStatus.UNBOUNDED, DcGatewayStatus.INAVAILABLE],
            message: '当前状态无法编辑'
        }
    ],
    natRuleCreate: [
        {
            required: false
        },
        {
            custom(data, options) {
                if (options.status !== DcGatewayStatus.RUNNING) {
                    return {
                        disable: true,
                        message: '仅可用状态的专线网关可添加规则'
                    };
                } else if (options.quota <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: `${options.title}配额不足`
                        };
                    } else {
                        return {
                            disable: true,
                            message: `${options.title}配额不足,如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    releaseNatRule: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    deleteL2gwConn: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行操作'
        }
    ]
};
