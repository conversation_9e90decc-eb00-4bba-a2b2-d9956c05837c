{"name": "console-network", "version": "1.0.0", "description": "百度智能云 | ", "main": "src/index.tsx", "scripts": {"dev": "cba-cli dev", "dev:mock": "export API_MOCK=true && cba-cli dev", "build:public": "npx cba-cli build --config bce-config.js", "build:xs": "export CONSOLE_TYPE=xs-console && cba-cli build", "build:private": "BUILD_TYPE=private cba-cli build", "build:coverage": "rimraf dist && COVERAGE=true cba-cli build --config bce-config.js", "i18n:extract": "cba-cli i18n:extract", "i18n:upload": "cba-cli i18n:upload", "lint": "eslint --ext .js,jsx,.ts,.tsx", "analyz": "NODE_ENV=production npm_config_report=true ANALYZ=true npm run build:public"}, "author": "zhanghao25", "license": "MIT", "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.18.9", "@babel/plugin-proposal-private-methods": "^7.18.6", "@baidu/cba-cli": "1.2.9-beta.10", "@baidu/cba-preset": "1.2.9-beta.10", "@baidu/cba-preset-console-react": "1.2.9-beta.10", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@types/lodash": "^4.14.202", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "autoprefixer": "^10.4.20", "babel-plugin-import": "^1.13.8", "babel-plugin-istanbul": "^6.1.1", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "7.31.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-prettier": "4.2.0", "eslint-plugin-simple-import-sort": "^10.0.0", "intro.js": "7.2.0", "mocker-api": "^3.0.2", "postcss": "^8.4.33", "postcss-loader": "^8.1.1", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "react-refresh": "^0.16.0", "tailwindcss": "^3.4.17", "webpack-bundle-analyzer": "4.9.0"}, "dependencies": {"@baidu/bce-ai-assist-sdk": "1.0.4", "@baidu/bce-bcc-sdk": "1.0.1-rc.37", "@baidu/bce-bcc-sdk-enum": "1.0.0-rc.21", "@baidu/bce-bcc-sdk-san": "1.0.0-rc.410", "@baidu/bce-eip-sdk": "1.0.6", "@baidu/bce-eip-sdk-san": "1.1.39-beta.2", "@baidu/bce-react-toolkit": "0.0.53-beta.20", "@baidu/bce-suggest-collection-sdk": "1.3.17", "@baidu/bce-vpc-sdk": "^1.0.6-beta.3", "@baidu/bce-vpc-sdk-react": "1.0.2-beta.86", "@baidu/bce-vpc-sdk-san": "1.0.6-beta.6", "@baidu/sui": "1.2.0-beta.39", "@baidu/sui-biz": "1.0.1-beta.18", "@baidu/sui-icon": "1.0.41", "@baidu/xicon-san": "0.0.43", "acud": "^1.4.53", "acud-icon": "^1.0.8", "driver.js": "0.9.8", "echarts": "^5.5.0", "jsplumb": "2.12.3", "lodash": "^4.17.21", "react": "^18.3.1", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-i18next": "^14.1.2", "react-query": "^3.39.3", "react-router-dom": "^6.22.1", "tailwind-merge": "^2.6.0", "typescript": "5.5.4", "xlsx": "0.16.6"}}