import u from 'lodash';
import {Ipv6Status} from '../../common/enum';
import {ContextService} from '../../common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

export default {
    createIpv6: [
        {
            custom(data, options) {
                if (options.createdIpv6Gateway <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '组播网关配额不足。'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `组播网关配额不足。如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    createSource: [
        {
            custom(data, options) {
                if (options.createdIpv6Gateway <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '当前组播网关下组播源配额不足。'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `当前组播网关下组播源配额不足。如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    createMember: [
        {
            custom(data, options) {
                if (options.createdIpv6Gateway <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '当前组播网关下组播成员配额不足。'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `当前组播网关下组播成员配额不足。如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选中实例'
        }
    ],
    ALTER_PRODUCTTYPE: [
        {
            status: [Ipv6Status.AVAILABLE],
            message(data) {
                if (data.length > 1) {
                    return '部分实例当前状态无法计费变更';
                }
                return '该实例当前状态无法计费变更';
            }
        },
        {
            orderAble: true,
            message: '该实例存在正在审核中的订单。如需操作，请稍后重试'
        },
        {
            custom(data) {
                if (u.chain(data).pluck('subProductType').uniq().value().length !== 1) {
                    return {
                        disable: true,
                        message: '选中实例的支付方式必须一致'
                    };
                }
            }
        }
    ],
    CANCEL_ALTER_PRODUCTTYPE: [
        {
            status: [Ipv6Status.AVAILABLE],
            message(data) {
                if (data.length > 1) {
                    return '部分实例当前状态不支持该操作，请稍候重试';
                }
                return '该实例当前状态不支持该操作，请稍候重试';
            }
        },
        {
            custom(data) {
                if (u.find(data, item => item.orderStatus !== 'shift_charge')) {
                    return {
                        disable: true,
                        message: '未进行计费变更无法进行该操作'
                    };
                }
            }
        }
    ],
    addQos: [
        {
            custom(data, options) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IPv6网关限速策略配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IPv6网关限速策略配额不足。如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    addSegment: [
        {
            custom(data, options) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'IPv6网关只出不进策略配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `IPv6网关只出不进策略配额不足。如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    changeResource: [
        {
            required: true,
            message: '请先选择实例对象'
        },
        {
            isSingle: true,
            message: '不支持批量变更'
        }
    ],
    deleteRules: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            custom(data, options) {
                if (
                    data &&
                    data.length &&
                    data.findIndex(item => item.multicastMembers?.length || item.multicastSources?.length) > -1
                ) {
                    return {
                        disable: true,
                        message: '请先删除实例的组播成员和组播源'
                    };
                }
            }
        }
    ]
};
