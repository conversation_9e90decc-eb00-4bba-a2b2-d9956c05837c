/*
 * @description: 安全组绑定实例
 * @file: dcgw/pages/nat/create.js
 * @author: p<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import u from 'lodash';
import {Component} from 'san';
import {html} from '@baiducloud/runtime';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {Input, Dialog, Pagination, Search, Tooltip, Table, Select, Button, Icon, Alert} from '@baidu/sui';
import {Empty} from '@baidu/sui-biz';

import {
    ddcTypeColumns,
    ddcRemarkColumns,
    ddcIpColumns,
    blbTypeColumns,
    scsTypeColumns,
    scsIpColumns,
    snicIpColumns,
    rdsTypeColumns,
    rdsDBColumns,
    rabbitMqColumns
} from './columns';

const IPSearch = /^[0-9.]+$/;
/* eslint-disable */
const tpl = html`
    <template>
        <s-dialog width="600" class="{{klass}}" closeAfterMaskClick="{{false}}" open="{=open=}" title="{{title}}">
            <div>
                <s-alert skin="warning">
                    <!--bca-disable-next-line-->
                    {{withHref | raw}}
                </s-alert>
                <div class="toolbar-line">
                    <span class="selectTip">{{selectTip}}</span>
                    <div class="opts inline_class">
                        <s-select
                            on-change="keywordTypeChange($event)"
                            datasource="{{keywordTypeList}}"
                            value="{=keywordType=}"
                        >
                        </s-select>
                        <s-input-search
                            placeholder="{{placeholder}}"
                            value="{=keyword=}"
                            on-search="onSearch"
                            on-input="inputSearch"
                        />
                        <div s-if="{{searchErr}}" class="search-err">{{searchErr}}</div>
                    </div>
                </div>
                <s-table
                    columns="{{table.columns}}"
                    loading="{{table.loading}}"
                    error="{{table.error}}"
                    datasource="{{table.datasource}}"
                    on-selected-change="tableSelected($event)"
                    selection="{=table.selection=}"
                    on-sort="onSort"
                >
                    <div slot="error">
                        <!--bca-disable-next-line-->
                        {{table.error ? table.error : '啊呀，出错了？' | raw}}
                        <a href="javascript:;" on-click="refresh">{{'重新加载'}}</a>
                    </div>
                    <div slot="empty">
                        <s-empty>
                            <div slot="action"></div>
                        </s-empty>
                    </div>
                </s-table>
                <s-pagination
                    s-if="{{pager.total}}"
                    class="pagination"
                    layout="{{'total, pageSize, pager, go'}}"
                    pageSize="{{pager.size}}"
                    total="{{pager.total}}"
                    page="{{pager.page}}"
                    pageSizes="{{pager.pageSizes}}"
                    on-pagerChange="onPageChange"
                    on-pagerSizeChange="onPageSizeChange"
                />
            </div>
            <div slot="footer">
                <div style="padding-left: 300px; padding-top:20px; display: inline-block;">
                    <s-button size="larger" on-click="cancel">{{'取消'}}</s-button>
                    <s-button disabled="{{!selectedItems.length || isConfirm}}" skin="primary" on-click="onBind"
                        >{{'确定'}}</s-button
                    >
                </div>
            </div>
        </s-dialog>
    </template>
`;
/* eslint-enable */
export default class Bind extends Component {
    static template = tpl;
    static components = {
        's-button': Button,
        's-alert': Alert,
        's-table': Table,
        's-textbox': Input,
        's-pagination': Pagination,
        's-input-search': Search,
        's-select': Select,
        's-dialog': Dialog,
        's-icon': Icon,
        's-tooltip': Tooltip,
        's-empty': Empty
    };
    static computed = {
        selectTip() {
            let type = this.data.get('type');
            if (type === 'bbc' || type === 'bcc') {
                return `已选中${this.data.get('table.selection.selectedIndex').length}条`;
            } else {
                return `已选中${this.data.get('table.selection.selectedIndex').length}条/共${this.data.get('pager.total')}条`;
            }
        },
        withHref() {
            let type = this.data.get('type');
            if (type === 'bcc') {
                return `温馨提示：普通安全组只展示普通安全组关联的实例，如需切换类型，请到BCC详情页操作安全组或批量操作请参考：<a href="https://cloud.baidu.com/doc/BCC/s/Tkaw7s5ec" blank="target">关联安全组</a>`;
            } else {
                return '温馨提示：普通安全组只展示普通安全组关联的实例';
            }
        }
    };
    initData() {
        return {
            klass: ['security-instance-list'],
            vpcId: '',
            selectedItems: [],
            title: '',
            emptyText: '暂无数据',
            keywordTypeList: [
                {text: '实例名称', value: 'name', placeholder: '请输入实例名称进行搜索'},
                {text: '实例ID', value: 'instanceId', placeholder: '请输入实例ID进行搜索'},
                {text: '实例内网IP', value: 'internalIp', placeholder: '请输入实例内网IP进行搜索'},
                {text: '实例公网IP', value: 'publicIp', placeholder: '请输入实例公网IP进行搜索'}
            ],
            placeholder: '请输入实例名称进行搜索',
            keywordType: 'name',
            keyword: '',
            table: {
                loading: false,
                selection: {
                    mode: 'multi',
                    selectedIndex: [],
                    disabledIndex(item) {
                        return item.associationNum >= 10;
                    }
                },
                columns: [
                    {
                        name: 'name',
                        label: '实例名称/ID',
                        width: 100,
                        render(item) {
                            let name = u.escape(item.name) || u.escape(item.scsName);
                            let id =
                                u.escape(item.id) ||
                                u.escape(item.eniId) ||
                                u.escape(item.blbShortId) ||
                                u.escape(item.instanceId) ||
                                u.escape(item.shortId) ||
                                u.escape(item.clusterId);
                            return `<span class="truncated" title="${name}">
                                        ${name}
                                    </span>
                                    </br><span class="truncated" title="${id}">${id}</span>`;
                        }
                    },
                    {
                        name: 'internalIp',
                        label: '实例IP',
                        width: 100,
                        render(item) {
                            if (item.eniId) {
                                let result = [];
                                u.each(item.ips, (data, index) => {
                                    if (index <= 2) {
                                        let ipData = FLAG.NetworkSupportEip
                                            ? (data.eip || '-') + '（公）' + '<br>' + (data.privateIp || '-') + '（内）'
                                            : (data.privateIp || '-') + '（内）';
                                        result.push(ipData);
                                    }
                                });

                                if (item.ips && item.ips.length > 3) {
                                    result.push('...');
                                }

                                return result.join('<br>');
                            } else if (item.blbType === 'ipv6') {
                                return FLAG.NetworkSupportEip ? '-(公)' + '<br>' + '-(内)' : '-(内)';
                            } else if (item.clusterId && item.lbInfos) {
                                let gaiaDbIps = [];
                                u.each(item.lbInfos, data => {
                                    gaiaDbIps.push(data.lbIp);
                                });
                                return gaiaDbIps.join('<br>');
                            } else {
                                return FLAG.NetworkSupportEip
                                    ? (item.publicIp || '-') + '（公）' + '<br>' + (item.internalIp || '-') + '（内）'
                                    : (item.internalIp || '-') + '（内）';
                            }
                        }
                    },
                    {
                        name: 'ipv6',
                        label: 'IPv6 IP',
                        width: 100,
                        render(item) {
                            return item.ipv6 || '-';
                        }
                    },
                    {
                        name: 'desc',
                        label: '描述',
                        width: 80,
                        render(item) {
                            let desc = u.escape(item.desc) || u.escape(item.description);
                            return desc || '-';
                        }
                    }
                ],
                datasource: []
            },
            pager: {
                size: 10,
                page: 1,
                total: 0,
                pageSizes: [10, 20, 50, 100]
            },
            order: {
                orderBy: 'name',
                order: 'desc'
            },
            searchErr: ''
        };
    }
    inited() {
        let type = this.data.get('type');
        // 设置不同实例id
        let idMap = {
            bcc: 'instanceId',
            bbc: 'instanceId',
            eni: 'eniId',
            blb: 'blbShortId',
            ddc: 'id',
            snic: 'shortId',
            scs: 'id',
            rds: 'shortId',
            rabbitmq: 'instanceId'
        };
        let instanceId = idMap[type] || 'id';
        this.data.splice('table.columns', [
            0,
            1,
            {
                name: 'name',
                label: '实例名称/ID',
                sortable: true,
                width: 100,
                render(item) {
                    let name = u.escape(item.name) || u.escape(item.scsName) || u.escape(item.instanceName);
                    let id = u.escape(item[instanceId]) || u.escape(item.clusterId);
                    return `<span class="truncated" title="${name}">
                            ${name}
                        </span>
                        </br><span class="truncated" title="${id}">${id}</span>`;
                }
            }
        ]);
        let title = '关联云服务器';
        if (type === 'eni') {
            title = '关联弹性网卡';
            this.data.splice('table.columns', [2, 1]);
        } else if (type === 'snic') {
            title = '关联服务网卡';
            this.data.splice('table.columns', [2, 1]);
            this.data.splice('table.columns', [1, 1, snicIpColumns]);
            this.data.set('pager.pageSizes', [10]);
            this.data.pop('keywordTypeList');
        } else if (type === 'bbc') {
            title = '关联弹性裸金属服务器';
            this.data.splice('table.columns', [2, 1]);
        } else if (type === 'blb') {
            title = '关联负载均衡';
            this.data.splice('table.columns', [1, 0, blbTypeColumns]);
        } else if (type === 'ddc') {
            title = '关联云数据库专属集群列表';
            this.data.splice('table.columns', [1, 0, ddcTypeColumns]);
            this.data.splice('table.columns', [2, 1, ddcIpColumns]);
            this.data.splice('table.columns', [3, 2]);
            this.data.splice('table.columns', [3, 0, ddcRemarkColumns]);
            this.data.pop('keywordTypeList');
        } else if (type === 'scs') {
            title = _('关联云数据库 Redis');
            this.data.splice('table.columns', [1, 0, scsTypeColumns]);
            this.data.splice('table.columns', [2, 1, scsIpColumns]);
            this.data.splice('table.columns', [3, 2]);
            this.data.push('keywordTypeList', {
                text: '实例类型',
                value: 'scsType',
                placeholder: '请输入实例类型进行搜索'
            });
        } else if (type === 'rds') {
            title = _('关联云数据库RDS');
            this.data.splice('table.columns', [1, 0, rdsTypeColumns]);
            this.data.splice('table.columns', [2, 1, rdsDBColumns]);
            this.data.splice('table.columns', [3, 2]);
            this.data.push('keywordTypeList', {
                text: '实例类型',
                value: 'remark',
                placeholder: '请输入实例类型进行搜索'
            });
            this.data.push('keywordTypeList', {
                text: '数据库类型',
                value: 'productType',
                placeholder: '请输入数据库类型进行搜索'
            });
        } else if (type === 'rabbitmq') {
            title = _('关联消息服务 for RabbitMQ');
            this.data.splice('table.columns', [1, 2]);
            this.data.splice('table.columns', [1, 0, rabbitMqColumns]);
            this.data.splice('keywordTypeList', [2, 2]);
            this.data.push('keywordTypeList', {text: '架构类型', value: 'type', placeholder: '请输入架构类型进行搜索'});
        } else if (type === 'gaiadb') {
            title = _('关联云数据库GaiaDB-S');
            this.data.set(
                'table.columns',
                this.data.get('table.columns').map(item => {
                    if (item.name === 'name') {
                        item.label = '集群名称/ID';
                    }
                    if (item.name === 'internalIp') {
                        item.label = '集群访问入口IP';
                        item.sortable = false;
                    }
                    return item;
                })
            );
            this.data.splice('table.columns', [2, 1]);
            this.data.splice('keywordTypeList', [0, 4]);
            this.data.set('placeholder', '请输入集群名称进行搜索');
            this.data.set('keywordTypeList', [
                {text: '集群名称', value: 'name', placeholder: '请输入集群名称进行搜索'},
                {text: '集群ID', value: 'instanceId', placeholder: '请输入集群ID进行搜索'},
                {text: '集群内网IP', value: 'internalIp', placeholder: '请输入集群内网IP进行搜索'},
                {text: '集群公网IP', value: 'publicIp', placeholder: '请输入集群公网IP进行搜索'}
            ]);
        } else if (type === 'bcc') {
            this.data.set(
                'keywordTypeList',
                this.data.get('keywordTypeList').map(item => {
                    if (item.value === 'internalIp') {
                        item.placeholder = '请输入实例内网IP进行精确搜索';
                    }
                    return item;
                })
            );
        }
        if (!FLAG.NetworkSupportEip) {
            let keywordTypeList = this.data.get('keywordTypeList');
            this.data.set(
                'keywordTypeList',
                keywordTypeList.filter(item => item.value !== 'publicIp')
            );
        }
        this.data.set('title', title);
    }
    keywordTypeChange(e) {
        this.data.get('keywordTypeList').filter(item => {
            if (item.value === e.value) {
                this.data.set('placeholder', item.placeholder);
            }
        });
    }
    onPageChange(e) {
        this.data.set('pager.page', e.value.page);
        this.data.set('table.selection.selectedIndex', []);
        this.loadPage();
    }
    onPageSizeChange(e) {
        this.data.set('pager.size', e.value.pageSize);
        if (this.data.get('pager.page') === 1) {
            this.loadPage();
        } else {
            this.data.set('pager.page', 1);
        }
    }
    tableSelected(e) {
        this.data.set('selectedItems', e.value.selectedItems);
    }
    // 重置列表
    resetTable() {
        this.data.set('pager.page', 1);
        this.data.set('table.selection.selectedIndex', []);
    }
    onSearch() {
        if (!this.data.get('searchErr')) {
            this.resetTable();
            this.loadPage();
        }
    }
    getSearchCriteria() {
        let {order} = this.data.get('');
        let pager = this.data.get('pager');
        let payload = {pageNo: pager.page, pageSize: pager.size, securityGroupId: this.data.get('id')};
        let keyword = this.data.get('keyword');
        if (keyword) {
            payload.keywordType = this.data.get('keywordType');
            payload.keyword = keyword;
        }
        payload.vpcId = this.data.get('vpcId');
        return u.extend({}, payload, order);
    }
    async loadPage() {
        this.data.set('table.loading', true);
        let payload = this.getSearchCriteria();
        let type = this.data.get('type');
        let result = null;
        try {
            if (type === 'eni') {
                result = await this.$http.getUnbindEniList(payload);
            } else if (
                type === 'blb' ||
                type === 'ddc' ||
                type === 'scs' ||
                type === 'rds' ||
                type === 'rabbitmq' ||
                type === 'gaiadb'
            ) {
                payload.serverType = type.toUpperCase();
                result = await this.$http.getUnbindSecInstanceList(payload);
            } else if (type === 'snic') {
                payload.serverType = type.toUpperCase();
                result = await this.$http.getUnbindSnicList(payload);
            } else {
                payload.vpcUuid = payload.vpcId;
                payload.instanceType = type.toUpperCase();
                payload.securityGroupUuid = payload.securityGroupId;
                delete payload.serverType;
                delete payload.vpcId;
                delete payload.pageNo;
                delete payload.pageSize;
                delete payload.securityGroupId;
                if (payload.keywordType === 'name') {
                    delete payload.keywordType;
                    payload.keywordType = 'instanceName';
                }
                result = await this.$http.getSecurityUnbindTopN(payload);
            }
            this.data.set('table.datasource', type === 'bbc' || type === 'bcc' ? result : result.result);
            type !== 'bbc' && type !== 'bcc' && this.data.set('pager.total', result.totalCount);
            this.data.set('table.loading', false);
        } catch (err) {
            this.data.set('table.datasource', []);
            this.data.set('table.loading', false);
        }
    }
    async onBind() {
        const type = this.data.get('type');
        let instanceUuids = this.data.get('selectedItems').map(item => {
            if (type === 'eni') {
                return item.eniUuid;
            } else if (type === 'blb') {
                return item.blbId;
            } else if (type === 'ddc' || type === 'scs' || type === 'rds' || type === 'rabbitmq') {
                return item.lbId;
            } else if (type === 'snic') {
                return item.shortId;
            } else if (type === 'gaiadb') {
                return item.clusterId;
            } else if (type === 'bcc') {
                return item.instanceUuid;
            } else if (type === 'bbc') {
                return item.instanceUuid;
            } else {
                return item.id;
            }
        });
        let payload = {
            instanceUuids,
            securityGroupUuids: [this.data.get('id')],
            instanceType: type.toUpperCase()
        };
        if (type === 'snic') {
            payload.vpcUuid = this.data.get('vpcId');
        }
        this.data.set('isConfirm', true);
        try {
            await this.$http.securityBindInstance(payload);
            this.data.set('open', false);
            this.fire('bind');
        } catch (err) {}
        this.data.set('isConfirm', false);
    }
    cancel() {
        this.data.set('open', false);
    }
    attached() {
        this.loadPage();
    }
    onSort(e) {
        let {value} = e;
        // 兼容内网Ip排序参数
        if (value.orderBy === 'ovip' || value.orderBy === 'ddcIp' || value.orderBy === 'lbIp') {
            value.orderBy = 'internalIp';
        }
        this.data.set('order', value);
        this.loadPage();
    }
    inputSearch({value}) {
        let keywordType = this.data.get('keywordType');
        if ((keywordType === 'internalIp' || keywordType === 'publicIp') && value && !IPSearch.test(value)) {
            this.data.set('searchErr', '输入IP格式有误');
        } else {
            this.data.set('searchErr', '');
        }
    }
}
