import u from 'lodash';
import {VpnStatus, PayType} from '@/pages/sanPages/common/enum';
import {ContextService} from '@/pages/sanPages/common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const currentModule = 'VPN';
export default {
    createVpn: [
        {
            required: false
        },
        {
            custom(data, options = {}) {
                if (!options.enableRegion) {
                    return {
                        disable: true,
                        message: `该地域暂不支持该功能。如需使用该功能，请提交
                        <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                    };
                }
                if (options.eipBlackList) {
                    return {
                        disable: true,
                        message: `禁止当前操作，请参照
                            <a href="http://security.baidu.com/ssp/web/#/require/work/detail?id=105&from=page" target="_blank">《百度内部业务上百度公有云安全规范》</a>要求处置`
                    };
                }
            }
        }
    ],
    recharge: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次进行续费'
        },
        {
            status: [VpnStatus.UN_CONFIG, VpnStatus.ACTIVE, VpnStatus.DOWN, VpnStatus.UPDATING],
            message: '该状态下的网关不支持续费。如需操作，请稍后重试'
        },
        {
            productType: ['prepay'],
            field: 'productType',
            message: '后付费实例无需进行续费操作'
        },
        {
            custom(data) {
                if (u.some(data, item => item.orderProductPayType)) {
                    return {
                        disable: true,
                        message: '计费变更中的网关不可续费。如需操作，请稍后重试'
                    };
                }
                if (u.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                            ${
                                FLAG.NetworkSupportXS
                                    ? '订单管理'
                                    : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                            }页面处理`
                    };
                }
            }
        }
    ],
    editTag: [
        {
            required: true,
            message: '请先选择实例'
        }
    ],
    RELEASE: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次释放'
        },
        {
            custom(data) {
                let availStatus = [VpnStatus.ACTIVE, VpnStatus.DOWN, VpnStatus.UN_CONFIG];
                if (
                    u.some(
                        data,
                        item => item.productType === PayType.PREPAY && new Date() < new Date(item?.expiredTime)
                    )
                ) {
                    return {
                        disable: true,
                        message: '预付费VPN网关在到期前不支持释放，到期后可以释放'
                    };
                }
                if (
                    u.some(data, item => item.productType !== PayType.PREPAY && availStatus.indexOf(item.status) === -1)
                ) {
                    return {
                        disable: true,
                        message: '当前网关状态不可释放'
                    };
                }
                if (u.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                            ${
                                FLAG.NetworkSupportXS
                                    ? '订单管理'
                                    : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                            }页面处理`
                    };
                }
                if (u.find(data, item => item.deleteProtect)) {
                    return {
                        disable: true,
                        message: '该实例开启了释放保护功能，请在实例详情页面中关闭释放保护后再点击释放'
                    };
                }
            }
        }
    ],
    ALTER_PRODUCTTYPE: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            status: [VpnStatus.ACTIVE, VpnStatus.UN_CONFIG, VpnStatus.UPDATING],
            message(data) {
                if (data.length > 1) {
                    return '部分网关当前状态不支持计费变更。如需操作，请稍后重试';
                }
                return '该状态下的网关不支持计费变更。如需操作，请稍后重试';
            }
        },
        {
            custom(data) {
                let productTypes = [];
                for (let i = 0; i < data.length; i++) {
                    if (data[i].orderProductPayType) {
                        return {
                            disable: true,
                            message: '计费变更中的网关不支持再次计费变更。如需操作，请稍后重试'
                        };
                    }
                    if (data[i].task === 'auto_renew') {
                        return {
                            disable: true,
                            message: `自动续费的网关不支持计费变更。如需操作，请先关闭计费变更，关闭计费请点击<a href="https://console.bce.baidu.com/billing/#/renew/list" target="_blank">续费</a>`
                        };
                    }
                    productTypes.push(data[i]);
                }
                let uniqLen = u
                    .chain(productTypes)
                    .map(item => {
                        return item.productType;
                    })
                    .uniq()
                    .value().length;
                if (uniqLen > 1) {
                    return {
                        disable: true,
                        message:
                            '计费变更已经支持预付费转后付费和后付费转预付费两种模式。<br>请您将预付费资源和后付费资源分开操作，谢谢！'
                    };
                }
                if (u.find(data, item => item.taskStatus === 'BUSY' || item.taskStatus === 'ORDER_BUSY')) {
                    return {
                        disable: true,
                        message: `当前账号有未完成订单，请前往
                            ${
                                FLAG.NetworkSupportXS
                                    ? '订单管理'
                                    : '<a href="/billing/#/order/list" target="_blank">财务中心-订单管理</a>'
                            }页面处理`
                    };
                }
            }
        }
    ],
    CANCEL_ALTER_PRODUCTTYPE: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            status: [VpnStatus.ACTIVE, VpnStatus.UN_CONFIG, VpnStatus.UPDATING],
            message(data) {
                return '未进行计费变更或计费变更已生效的实例不能取消计费变更操作';
            }
        },
        {
            custom(data) {
                if (u.some(data, item => !item.orderProductPayType)) {
                    return {
                        disable: true,
                        message: '未进行计费变更无法进行该操作'
                    };
                }
            }
        }
    ],
    addVpnConn: [
        {
            required: false
        },
        {
            status: [VpnStatus.ACTIVE],
            message(data) {
                return data.status === 'unconfigured'
                    ? '请先绑定IP，再进行创建隧道操作'
                    : '该状态的网关不支持创建隧道。如需操作，请稍后重试';
            }
        },
        {
            custom(data, options = {}) {
                if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'VPN的隧道配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `VPN的隧道配额不足，<a href="/quota_center/#/quota/apply/create?serviceType=${currentModule}&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=vpnConnQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    addSslServer: [
        {
            required: false
        },
        {
            status: [VpnStatus.ACTIVE],
            message(data) {
                return data.status === 'unconfigured'
                    ? '请先绑定IP，再进行创建隧道操作'
                    : '该状态的网关不支持创建隧道。如需操作，请稍后重试';
            }
        },
        {
            custom(data, options = {}) {
                if (options.serverNum > 0) {
                    return {
                        disable: true,
                        message: '一个网关下只能创建一个服务端'
                    };
                } else {
                    return {
                        disable: false,
                        message: ''
                    };
                }
            }
        }
    ],
    deleteVpnConn: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次删除'
        }
    ],
    deleteSslVpnConn: [
        {
            required: true,
            message: '请先选择实例'
        },
        {
            isSingle: true,
            message: '不支持批量操作，请依次删除'
        },
        {
            custom(data, options = {}) {
                if (options.userCount > 0) {
                    return {
                        disable: true,
                        message: '当前服务端下有用户，请先删除用户后再尝试删除服务端'
                    };
                }
            }
        }
    ],
    bindEip: [
        {
            required: false
        },
        {
            custom(data, options = {}) {
                let statusData = [VpnStatus.BUILDING, VpnStatus.UPDATING, VpnStatus.DELETING];
                if (options.eipBlackList) {
                    return {
                        disable: true,
                        message: `禁止当前操作，请参照
                            <a href="http://security.baidu.com/ssp/web/#/require/work/detail?id=105&from=page" target="_blank">《百度内部业务上百度公有云安全规范》</a>要求处置`
                    };
                }
                if (data[0] && statusData.includes(data[0].status)) {
                    return {
                        disable: true,
                        message: '该状态下的网关不支持绑定eip操作'
                    };
                }
            }
        }
    ],
    createLocalSubNet: [
        {
            custom(data, {total = 10}) {
                if (data.length >= total) {
                    return {
                        disable: true,
                        message: `最多只能添加${total}个本端网络！`
                    };
                }
            }
        }
    ],
    createRemoteSubNet: [
        {
            custom(data, {total = 10}) {
                if (data.length >= total) {
                    return {
                        disable: true,
                        message: `最多只能添加${total}个对端网络！`
                    };
                }
            }
        }
    ],
    natRuleCreate: [
        {
            required: false
        },
        {
            custom(data, {quota, status, hasVpnConn, title}) {
                if (!hasVpnConn) {
                    return {
                        disable: true,
                        message: '未创建VPN隧道'
                    };
                }
                if (status !== VpnStatus.ACTIVE) {
                    return {
                        disable: true,
                        message: '仅可用状态的VPN网关可添加规则'
                    };
                } else if (quota.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: `最多可添加${quota.total}条规则`
                        };
                    } else {
                        return {
                            disable: true,
                            message: `最多可添加${quota.total}条规则。如需增加VPN${title}配额请提交
                            <a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                } else {
                    return {
                        disable: false,
                        message: ''
                    };
                }
            }
        }
    ],
    releaseNatRule: [
        {
            required: true,
            message: '请先选择实例对象'
        }
    ],
    createSslUserRule: [
        {
            required: false
        },
        {
            custom(data, options = {}) {
                if (options.quota <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '创建ssl用户配额不足'
                        };
                    } else {
                        return {
                            disable: true,
                            message: `创建ssl用户配额已超默认配额50，如需增加，建议在SSL VPN网关详情页进行SSL 连接数升级后再申请用户数配额调整，否则会影响同时在线用户登录。<a href="/quota_center/#/quota/apply/create?serviceType=${currentModule}&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=SslVpnUserQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ]
};
