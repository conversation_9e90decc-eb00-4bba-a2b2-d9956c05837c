/* eslint-disable @typescript-eslint/member-ordering */
/**
 * @file NAT网关创建
 * <AUTHOR>
 */
import _ from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';
import {ShoppingCart, OrderConfirm, TotalPrice, Protocol} from '@baiducloud/bce-billing-sdk-san';
import {BillingSdk, OrderItem, UPDATE_STATUS} from '@baiducloud/bce-billing-sdk';
import Client from '@baiducloud/httpclient';
import {ResourceGroupPanel} from '@baidu/bce-resourcegroup-sdk-san';
import {ResourceGroupSDK} from '@baidu/bce-resourcegroup-sdk';
import {TagEditPanel} from '@baidu/bce-tag-sdk-san';
import {checker} from '@baiducloud/bce-opt-checker';
import {OutlinedQuestion} from '@baidu/sui-icon';
import {NAT_ENHANCED_MAPPING} from '@/pages/sanPages/common/enum';
import rules from '../rules';
import Assist from '@/utils/assist';
import NatEip from '../components/eip';

import {
    PayType,
    NatFlavor,
    TimeType,
    Month,
    NatType,
    Year,
    EipType,
    NatClusterMode,
    DocService
} from '@/pages/sanPages/common';
import Rule from '@/pages/sanPages/utils/rule';
import {
    showMoney,
    contextPipe,
    getVPCSupportRegion,
    getUserId,
    $flag as FLAG,
    intersectionBy,
    decryptUrlBtoa
} from '@/pages/sanPages/utils/helper';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {isOnline} from '@/pages/sanPages/utils/common';
import zone from '@/pages/sanPages/utils/zone';
import testID from '@/testId';
import {updateResource} from '@/apis/nat';
import './create.less';

const AllRegion = window.$context.getEnum('AllRegion');
const natRoleName = StsConfig.NAT.roleName;

const {invokeSUI, invokeAppComp, invokeSUIBIZ, invokeComp, template, service, asComponent} = decorators;

const tpl = html`
    <div class="create-nat-wrap">
        <s-app-create-page
            pageTitle="{{pageNavTitle}}"
            backToLabel="{{backLabel}}"
            backTo="{{backUrl}}"
            style="display: {{confirming ? 'none' : 'block'}}"
        >
            <div class="s-step-block">
                <s-steps current="{{currentSteps}}">
                    <s-steps-step s-for="item in steps" title="{{item.text}}" />
                </s-steps>
            </div>
            <s-form s-ref="form" data="{=formData=}" rules="{{rules}}" label-align="left">
                <div class="body-part-content form-part-wrap">
                    <h4>付费及地域</h4>
                    <s-form-item prop="productType" label="付费方式：">
                        <div class="productType-wrap">
                            <div
                                on-click="productTypeChange(item.value)"
                                s-for="item in datasource.productType"
                                class="productType-item {{item.value === formData.productType && 'actice-mode'}}"
                            >
                                <img class="productType-img" src="{{item.img}}" alt="" />
                                <div class="productType-content-wrap">
                                    <span class="productType-text">{{item.text}}</span>
                                    <span class="productType-content">{{item.content}}</span>
                                </div>
                            </div>
                        </div>
                    </s-form-item>
                    <s-form-item prop="region" label="当前地域：" class="body-part-content-region s-form-item-region">
                        <template slot="label" class="label_class">
                            {{'当前地域：'}}
                            <s-tip placement="top" class="inline-tip">
                                <s-question class="question-class warning-class"></s-question>
                                <span slot="content">
                                    如需修改购买其他区域产品，请{{isXSTip}}。
                                    <a
                                        class="assist-tip"
                                        href="javascript:void(0)"
                                        on-click="showAssist('region')"
                                        s-if="FLAG.NetworkSupportAI"
                                        >了解详情</a
                                    >
                                </span>
                            </s-tip>
                        </template>
                        <s-radio-radio-group
                            enhanced
                            datasource="{{datasource.region}}"
                            value="{=formData.region=}"
                            radioType="button"
                            on-change="onRegionChange"
                            class="region-radio-group"
                        />
                    </s-form-item>
                    <h4 class="form-part-title">配置信息</h4>
                    <s-form-item class="network-subnet-widget" prop="vpcId" label="所在网络：">
                        <s-select
                            on-change="vpcChange"
                            width="{{260}}"
                            value="{=formData.vpcId=}"
                            placeholder="请选择所在网络"
                        >
                            <s-select-option
                                s-for="item in vpcs"
                                key="{{item.value}}"
                                value="{{item.value}}"
                                label="{{item.text}}"
                            >
                                <s-tooltip>
                                    <div slot="content">{{item.text}}</div>
                                    <div>{{item.text}}</div>
                                </s-tooltip>
                            </s-select-option>
                        </s-select>
                        <div s-if="isPrivate" class="subnetId-wrapper">
                            <s-form-item label="" prop="subnetId">
                                <s-select
                                    width="{{300}}"
                                    disabled="{{loading}}"
                                    value="{=formData.subnetId=}"
                                    on-change="handleSubnetChange"
                                    data-test-id="${testID.nat.createSubnetSelect}"
                                >
                                    <s-select-option
                                        s-for="item in subnetDatasource"
                                        value="{{item.value}}"
                                        label="{{item.text}}"
                                        disabled="{{item.soldoutTip}}"
                                        data-test-id="${testID.nat.createSubnetSelect}-{{index}}"
                                    >
                                        <s-tooltip>
                                            <div slot="content">
                                                <span s-if="item.soldoutTip">
                                                    {{'当前子网对应的可用区实例资源售罄，请选择其他可用区的子网或创建新的可用区'}}
                                                    <a href="#/vpc/subnet/list">{{'子网'}}</a>
                                                </span>
                                                <span s-else>{{item.text}}</span>
                                            </div>
                                            <div>{{item.text}}</div>
                                        </s-tooltip>
                                    </s-select-option>
                                </s-select>
                                <div s-if="formData.subnetId" slot="help">
                                    <div s-if="!noneIpTip" class="common-tip zone-tip" s-html="zoneTipContent"></div>
                                    <div s-else class="common-tip none-ip-tip">
                                        <!--bca-disable-next-line-->
                                        {{noneIpTip | raw}}
                                    </div>
                                </div>
                            </s-form-item>
                        </div>
                        <div class="tip-wrap" s-if="getTip !== '' && !FLAG.NetworkNatOpt">{{getTip}}</div>
                    </s-form-item>
                    <s-form-item prop="name" label="NAT网关名称：" help="{{natRule.NAME.placeholder}}">
                        <s-input
                            placeholder="请输入NAT网关名称"
                            value="{=formData.name=}"
                            width="{{260}}"
                            data-test-id="${testID.nat.createNameInput}"
                        />
                    </s-form-item>
                    <s-form-item s-if="{{!isPrivate}}" prop="flavor" label="类型：" class="body-part-content-flavor">
                        <div class="normal-flavor-wrapper">
                            <s-radio-radio-group
                                value="{=formData.clusterMode=}"
                                radioType="button"
                                datasource="{{datasource.clusterMode}}"
                                class="nat-mode-wrap"
                                enhanced
                            >
                            </s-radio-radio-group>
                            <s-tip s-if="{{formData.clusterMode}}" class="enhance-inline-tip" placement="top">
                                <s-question class="question-class warning-class"></s-question>
                                <div slot="content">增强型NAT网关可提供更高的性能容量,无损升级性能容量。</div>
                            </s-tip>
                        </div>
                        <div class="normal-flavor-tip" s-if="{{formData.clusterMode === false}}">
                            <s-radio-radio-group
                                value="{=formData.flavor=}"
                                radioType="button"
                                class="nat-purchase-wrap"
                                enhanced
                            >
                                <span s-for="item, index in flavorList" class="nat_create_sellOut">
                                    <s-popover s-if="item.sellOut">
                                        <div slot="content">{{'售罄！请您移步其他地域购买资源。'}}</div>
                                        <s-badge>
                                            <s-radio
                                                value="{{item.value}}"
                                                label="{{item.label}}"
                                                disabled="{{true}}"
                                                class="{{(index === 0 && 'radius-left') || (index === flavorList.length - 1 && 'radius-right')}}"
                                            >
                                            </s-radio>
                                        </s-badge>
                                    </s-popover>
                                    <s-radio
                                        s-else
                                        value="{{item.value}}"
                                        label="{{item.label}}"
                                        class="{{(index === 0 && 'radius-left') || (index === flavorList.length - 1 && 'radius-right')}}"
                                    >
                                    </s-radio>
                                </span>
                            </s-radio-radio-group>
                            <div s-if="{{formData.flavor !== 'enhanced_12c6q'}}" class="inline-tip">
                                {{formData.flavor | getFlavorTip}}
                            </div>
                        </div>
                    </s-form-item>
                    <s-form-item
                        prop="cuNum"
                        label="性能容量："
                        class="center_class"
                        s-if="{{isEnhanced || isPrivate}}"
                    >
                        <div class="slider-wrapper">
                            <s-slider
                                ref="drag"
                                parts="{{2}}"
                                marks="{{marks}}"
                                max="{{datasource.cuNum.max}}"
                                min="{{datasource.cuNum.min}}"
                                step="{{datasource.cuNum.step}}"
                                value="{=formData.cuNum=}"
                            />
                            <div class="dragger-input">
                                <s-input-number
                                    value="{=formData.cuNum=}"
                                    max="{{datasource.cuNum.max}}"
                                    min="{{datasource.cuNum.min}}"
                                />
                                CU
                            </div>
                        </div>
                        <p class="slider-info">
                            {{natPerformanceText}}
                            <a
                                class="assist-tip"
                                href="javascript:void(0)"
                                on-click="showAssist('flavor')"
                                s-if="FLAG.NetworkSupportAI"
                                >了解详情</a
                            >
                        </p>
                    </s-form-item>
                    <s-form-item
                        prop="bindEips"
                        label="公网IP："
                        s-if="formData.clusterMode && !isPrivate"
                        class="center_class"
                    >
                        <nat-eip
                            natMultiEip="{{natMultiEip}}"
                            s-ref="bindEips"
                            eips="{=formData.bindEips=}"
                            nat-type="${NatType.SNAT}"
                            is-bind="{{true}}"
                            flavor="{=formData.flavor=}"
                            clusterMode="{=formData.clusterMode=}"
                            cuNum="{=formData.cuNum=}"
                            quota="{=formData.bindEipsQuota=}"
                            hideNormalType="{{hideNormalType}}"
                            on-change="onNatChange('bindEips', $event)"
                        />
                    </s-form-item>
                    <s-form-item
                        prop="snat"
                        label="SNAT公网IP："
                        s-if="!formData.clusterMode && !isPrivate"
                        class="center_class"
                    >
                        <nat-eip
                            s-ref="snat"
                            eips="{=formData.eips=}"
                            nat-type="${NatType.SNAT}"
                            is-bind="{{true}}"
                            flavor="{=formData.flavor=}"
                            clusterMode="{=formData.clusterMode=}"
                            cuNum="{=formData.cuNum=}"
                            quota="{=formData.snatQuota=}"
                            hideNormalType="{{hideNormalType}}"
                            on-change="onNatChange('snat', $event)"
                        />
                    </s-form-item>
                    <s-form-item
                        prop="dnat"
                        label="DNAT公网IP："
                        s-if="!natBlackList && !formData.clusterMode && !isPrivate"
                        class="center_class"
                    >
                        <nat-eip
                            s-ref="dnat"
                            eips="{=formData.dnatEips=}"
                            eip="{{''}}"
                            nat-type="${NatType.DNAT}"
                            is-bind="{{true}}"
                            flavor="{=formData.flavor=}"
                            clusterMode="{=formData.clusterMode=}"
                            cuNum="{=formData.cuNum=}"
                            quota="{=formData.dnatQuota=}"
                            hideNormalType="{{hideNormalType}}"
                            on-change="onNatChange('dnat', $event)"
                        />
                    </s-form-item>
                    <s-app-legend class="legend-wrap" label="连接超时时间" s-if="formData.clusterMode && !isPrivate">
                        <s-icon
                            class="{{showAdvance ? 'advance-icon actived' : 'advance-icon'}}"
                            name="xialajiantou"
                            slot="extra"
                            on-click="showConfigure"
                        ></s-icon>
                    </s-app-legend>
                    <template s-if="showAdvance && formData.clusterMode && !isPrivate">
                        <s-form-item prop="tcpTimeOut" label="TCP连接超时时间：" help="输入范围：【10，4000】整数">
                            <s-input-number value="{=formData.tcpTimeOut=}" max="4000" width="140" min="10" step="1" />
                        </s-form-item>
                        <s-form-item prop="udpTimeOut" label="UDP连接超时时间：" help="输入范围：【5，4000】整数">
                            <s-input-number value="{=formData.udpTimeOut=}" max="4000" width="140" min="5" step="1" />
                        </s-form-item>
                        <s-form-item prop="icmpTimeOut" label="ICMP连接超时时间：" help="输入范围：【5，4000】整数">
                            <s-input-number value="{=formData.icmpTimeOut=}" max="4000" width="140" min="5" step="1" />
                        </s-form-item>
                    </template>
                    <s-form-item prop="description" label="描述：">
                        <s-input-text-area
                            placeholder="请输入描述"
                            value="{=formData.description=}"
                            width="400"
                            height="80"
                            maxLength="{{200}}"
                        />
                    </s-form-item>
                    <h4>标签</h4>
                    <s-form-item prop="tag" label="绑定标签：">
                        <tag-edit-panel
                            create
                            version="v2"
                            instances="{{defaultInstances}}"
                            options="{{tagListRequster}}"
                            s-ref="tagPanel"
                        />
                    </s-form-item>
                    <resource-group-panel
                        refreshAvailable="{{true}}"
                        sdk="{{resourceSDK}}"
                        on-change="resourceChange($event)"
                        class="form-part-title {{!FLAG.NetworkSupportXS ? 'xs-hidden-resource-create' : ''}}"
                    />
                    <div class="body-part-content form-body-wrapper">
                        <h4 s-if="!isPrivate">购买信息</h4>
                        <s-form-item prop="purchaseLength" label="购买时长：" s-if="isPrepay">
                            <s-tag-radio-group
                                radioType="button"
                                value="{=formData.purchaseLength=}"
                                datasource="{{datasource.purchaseLength}}"
                                class="purchase-btn region-radio-group"
                            >
                            </s-tag-radio-group>
                        </s-form-item>
                        <s-form-item
                            prop="autoRenew"
                            label="自动续费："
                            s-if="isPrepay && FLAG.NetworkNatOpt && !FLAG.NetworkSupportXS"
                            class="center_class"
                        >
                            <template slot="label">
                                {{'自动续费：'}}
                                <s-tip placement="top" class="inline-tip">
                                    <s-question class="question-class warning-class"></s-question>
                                    <span slot="content">
                                        开通自动续费后，百度智能云将在实例到期前7/3/1/0天定时进行自动扣款续费，续费成功或失败都将向您发送短信和邮件提醒。
                                        <a
                                            class="assist-tip"
                                            href="javascript:void(0)"
                                            on-click="showAssist()"
                                            s-if="FLAG.NetworkSupportAI"
                                            >了解详情</a
                                        >
                                    </span>
                                </s-tip>
                            </template>
                            <div class="autoRenew-wrapper">
                                <s-radio-radio-group
                                    value="{=formData.autoRenew=}"
                                    radioType="button"
                                    datasource="{{datasource.autoRenewList}}"
                                    class="nat-mode-wrap button-mode"
                                >
                                </s-radio-radio-group>
                            </div>
                        </s-form-item>
                        <s-form-item s-if="isPrepay && formData.autoRenew" label="{{'选择续费周期：'}}">
                            <div class="autorenew-panel">
                                <s-select
                                    datasource="{{datasource.autoRenewTimeUnit}}"
                                    value="{=formData.autoRenewTimeUnit=}"
                                />
                                <s-select
                                    datasource="{{datasource.autoRenewTime}}"
                                    value="{=formData.autoRenewTime=}"
                                />
                                <div class="renew-expire-time">{{autoRenewTip}}</div>
                            </div>
                        </s-form-item>
                        <s-form-item s-if="!isPrivate" label="释放保护：" class="center_class">
                            <div class="autoRenew-wrapper">
                                <s-radio-radio-group
                                    value="{=deleteProtect=}"
                                    radioType="button"
                                    datasource="{{datasource.deleteProtectList}}"
                                    class="nat-mode-wrap button-mode"
                                >
                                </s-radio-radio-group>
                            </div>
                        </s-form-item>
                    </div>
                </div>
            </s-form>
            <div class="buybucket" slot="pageFooter">
                <div class="buybucket-container">
                    <s-tooltip placement="top" trigger="{{natSinDisable.disable || bucket.disabled ? 'hover' : ''}}">
                        <!--bca-disable-next-line-->
                        <span slot="content" s-html="{{natSinDisable.message || bucket.tip}}"></span>
                        <s-button
                            skin="primary"
                            size="large"
                            on-click="onConfirm"
                            class="confirm-btn"
                            track-id="vpc_nat_confirm_order"
                            disabled="{{natSinDisable.disable || bucket.disabled || priceLoading}}"
                            data-test-id="${testID.nat.createConfirmOrder}"
                        >
                            {{'确认订单'}}
                        </s-button>
                    </s-tooltip>
                    <s-tooltip
                        placement="top"
                        trigger="{{(bucket.disabled ||formData.clusterMode) ? 'hover' : ''}}"
                        s-if="{{formData.productType === 'prepay' && !FLAG.NetworkSupportXS}}"
                    >
                        <!--bca-disable-next-line-->
                        <span slot="content" s-html="{{bucket.tip || '增强型NAT暂不支持加入购物车'}}"></span>
                        <s-button
                            size="large"
                            on-click="addShoppingCart"
                            track-id="vpc_nat_add_cart"
                            disabled="{{bucket.disabled || cartConfirming || formData.clusterMode || priceLoading}}"
                        >
                            {{'加入购物车'}}
                        </s-button>
                    </s-tooltip>
                    <s-button size="large" on-click="cancel">取消</s-button>
                    <shopping-cart
                        sdk="{{newBillingSdk}}"
                        on-reset="onReset"
                        addItemToCartAvailable="{{addItemToCartAvailable}}"
                        addItemToCartDisable="{=priceLoading=}"
                        on-change="onShoppingCartChange"
                        theme="default"
                    ></shopping-cart>
                </div>
            </div>
        </s-app-create-page>
        <s-app-create-page
            s-if="confirming"
            pageTitle="{{pageNavTitle}}"
            backToLabel="{{backLabel}}"
            backTo="{{backUrl}}"
        >
            <div class="s-step-block">
                <s-steps current="{{currentSteps}}">
                    <s-steps-step s-for="item in steps" title="{{item.text}}" />
                </s-steps>
            </div>
            <order-confirm
                s-ref="orderConfirm"
                sdk="{{newBillingSdk}}"
                use-coupon="{{isPrepay && FLAG.NetworkSupportXS}}"
                theme="default"
                showAgreementCheckbox
                class="nat-order-wrap"
            />
            <div class="buybucket" slot="pageFooter">
                <div class="buybucket-container">
                    <div class="buybucket-container-protocol">
                        <billing-protocol s-if="!FLAG.NetworkSupportXS" s-ref="billingProtocol" />
                        <s-button
                            size="large"
                            class="confirm-btn"
                            on-click="onBack"
                            data-test-id="${testID.nat.createBackPre}"
                            >{{'上一步'}}</s-button
                        >
                        <s-button size="large" on-click="cancel" data-test-id="${testID.nat.createCancel}"
                            >取消</s-button
                        >
                        <s-button
                            skin="primary"
                            disabled="{{disableSub}}"
                            size="large"
                            on-click="onPay"
                            track-id="vpc_nat_submit_order"
                            data-test-id="${testID.nat.createSubmitOrder}"
                        >
                            {{'提交订单'}}
                        </s-button>
                    </div>
                    <total-price sdk="{{newBillingSdk}}" />
                </div>
            </div>
        </s-app-create-page>
    </div>
`;

@asComponent('@nat-create')
@invokeSUI
@invokeSUIBIZ
@invokeAppComp
@template(tpl)
class NatCreate extends Component {
    static components = {
        'order-confirm': OrderConfirm,
        'shopping-cart': ShoppingCart,
        'resource-group-panel': ResourceGroupPanel,
        'total-price': TotalPrice,
        'billing-protocol': Protocol,
        'tag-edit-panel': TagEditPanel,
        's-question': OutlinedQuestion,
        'nat-eip': NatEip
    };

    initData() {
        const selfThis = this;
        const region = this.$context.getCurrentRegion();
        return {
            FLAG,
            DocService,
            backUrl: '/network/#/vpc/nat/list',
            backLabel: '返回',
            pageNavTitle: '创建 NAT网关',
            datasource: {
                productType: [
                    {
                        text: '包年包月',
                        value: PayType.PREPAY,
                        content: '先付费后使用，价格更低廉',
                        img: 'http://bce.bdstatic.com/network-frontend/prepay.png'
                    },
                    {
                        text: '按量付费',
                        value: PayType.POSTPAY,
                        content: '先使用后付费，按需开通',
                        img: 'http://bce.bdstatic.com/network-frontend/postpay.png'
                    }
                ],
                clusterMode: NatClusterMode.toArray(),
                region: [
                    {
                        text: region.label,
                        value: region.id
                    }
                ],
                cuNum: {
                    min: 1,
                    max: 100,
                    mid: 50,
                    step: 1,
                    unit: 'CU',
                    value: 1
                },
                flavor: NatFlavor.toArray(),
                autoRenewTimeUnit: TimeType.toArray(),
                autoRenewTime: Month.toArray(),
                purchaseLength: [
                    {text: '1个月', value: 1},
                    {text: '2个月', value: 2},
                    {text: '3个月', value: 3},
                    {text: '4个月', value: 4},
                    {text: '5个月', value: 5},
                    {text: '6个月', value: 6},
                    {text: '7个月', value: 7},
                    {text: '8个月', value: 8},
                    {text: '9个月', value: 9},
                    {text: '1年', value: 12, mark: '8.3折'},
                    {text: '2年', value: 24, mark: '8.3折'},
                    {text: '3年', value: 36, mark: '8.3折'}
                ],
                autoRenewList: [
                    {text: '关闭', value: false},
                    {text: '开启', value: true}
                ],
                deleteProtectList: [
                    {text: '开启', value: true},
                    {text: '关闭', value: false}
                ]
            },
            rules: {
                vpcId: [{required: true, message: '请选择所在网络'}],
                name: [
                    {required: true, message: '请填写名称'},
                    {pattern: Rule.NAT.NAME.pattern, message: Rule.NAT.NAME.patternErrorMessage}
                ],
                bindEips: [
                    {
                        validator: (rule, value, callback, source) => {
                            const formData = selfThis.data.get('formData');
                            if (formData.bindEips.length > formData.bindEipsQuota.free) {
                                return callback(formData.bindEipsQuota.errorMessage);
                            }
                            callback();
                        }
                    }
                ],
                snat: [
                    {
                        validator: (rule, value, callback, source) => {
                            const formData = selfThis.data.get('formData');
                            if (_.intersection(formData.eips, formData.dnatEips).length) {
                                return callback('DNAT和SNAT不能共用一个公网IP');
                            }
                            if (formData.eips.length + formData.dnatEips.length > formData.snatQuota.free) {
                                return callback(formData.snatQuota.errorMessage);
                            }
                            callback();
                        }
                    }
                ],
                dnat: [
                    {
                        validator: (rule, value, callback, source) => {
                            const formData = selfThis.data.get('formData');
                            if (_.intersection(formData.dnatEips, formData.eips).length) {
                                return callback('DNAT和SNAT不能共用一个公网IP');
                            }
                            if (formData.eips.length + formData.dnatEips.length > formData.dnatQuota.free) {
                                return callback(formData.dnatQuota.errorMessage);
                            }
                            callback();
                        }
                    }
                ],
                subnetId: [{required: true, message: '请选择所在子网'}],
                tcpTimeOut: [{required: true, message: '请填写TCP连接超时时间'}],
                udpTimeOut: [{required: true, message: '请填写UDP连接超时时间'}],
                icmpTimeOut: [{required: true, message: '请填写ICMP连接超时时间'}]
            },
            formData: this.getInitFormData(NatFlavor.toArray()),
            natRule: Rule.NAT,
            bucket: {
                price: [],
                datasource: [],
                confirmText: '确认订单',
                disabled: false
            },
            confirm: {
                previous: true
            },
            natBlackList: true,
            disableSub: false,
            resourceSDK: {},
            flavorList: [],
            bucketItems: [],
            bucketPrice: [],
            natSinDisable: {},
            // 初始类型
            clusterModeInit: false,
            steps: [{text: '基本配置'}, {text: '确认订单'}],
            currentSteps: 1,
            priceLoading: true,
            confirmedPay: false,
            addItemToCartAvailable: false,
            loadNeed: false,
            deleteProtect: false,
            defaultInstances: [
                {
                    tags: [
                        {
                            tagKey: '默认项目',
                            tagValue: ''
                        }
                    ]
                }
            ],
            tagListRequster: this.tagListRequster.bind(this),
            natType: '',
            currZone: '',
            availableIPs: 0,
            subnetDatasource: [],
            loading: false,
            noneIpTip: '',
            showAdvance: false,
            urlQuery: getQueryParams(window.location.href)
        };
    }

    static filters = {
        getFlavorTip(item) {
            return NatFlavor.fromValue(item).desc || '';
        },
        getYear(value) {
            return `注：购买${value / 12}年8.3折`;
        }
    };

    static computed = {
        isPrepay() {
            return this.data.get('formData.productType') === PayType.PREPAY;
        },
        getTip() {
            let vpcList = this.data.get('vpcs');
            if (!vpcList || vpcList.length === 0) {
                return '所选项目下没有可用的私有网络VPC实例，请先创建所选项目的VPC实例或变更已有VPC实例的项目属性';
            }
            return '';
        },
        marks() {
            let marks = {};
            let min = this.data.get('datasource.cuNum.min');
            let max = this.data.get('datasource.cuNum.max');
            let middle = _.round(max / 2);
            marks[min] = min + 'CU';
            marks[max] = max + 'CU';
            marks[middle] = middle + 'CU';
            return marks;
        },
        isPrivate() {
            const natType = this.data.get('natType');
            return natType === 'private';
        },
        isXSTip() {
            return !FLAG.NetworkSupportXS ? '前往主导航进行切换' : '在顶栏重新选择区域';
        },
        isEnhanced() {
            const clusterMode = this.data.get('formData.clusterMode');
            return ['v4', 'v6'].includes(clusterMode);
        },
        isEnhancedIPv6() {
            const clusterMode = this.data.get('formData.clusterMode');
            return clusterMode === 'v6';
        },
        zoneTipContent() {
            const currZone = this.data.get('currZone');
            const availableIPs = this.data.get('availableIPs');
            const isXS = this.data.get('FLAG.NetworkSupportXS');

            if (!isXS) {
                return `当前${currZone}，可用IP共${availableIPs}个；如需创建子网，您可以到<a href="#/vpc/subnet/list" target="_blank">私有网络-子网</a>去创建`;
            } else {
                return `当前${currZone}，可用IP共${availableIPs}个；如需创建子网，您可以到私有网络-子网去创建`;
            }
        },
        natPerformanceText() {
            const cuNum = this.data.get('formData.cuNum');
            return `NAT网关性能容量参数：新建连接数${cuNum * 1000}个/秒、并发连接数${cuNum * 10000}个、转发能力${cuNum}Gbps`;
        }
    };

    static messages = {
        projectChange({value}) {
            value && this.data.set('resourceGroupId', value);
            let allVpc = this.data.get('allVpc');
            let vpcs = _.map(this.filterVpcList(allVpc, value), item => ({
                text: `${item.name}（${item.cidr}）`,
                value: item.vpcId
            }));
            this.data.set('vpcs', vpcs);
            this.setVpcId(vpcs);
        }
    };

    async inited() {
        // 从 URL 获取传递的表单信息
        this.getConfigForm();
        const natType = this.data.get('route').natType;
        this.initPrivateNatConfig(natType);
        const client = new Client({}, {}.$context);
        // 创建实例
        const sdk = new BillingSdk({
            client,
            AllRegion: window.$context.getEnum('AllRegion'),
            context: contextPipe(this)
        });
        this.data.set('newBillingSdk', sdk);
        if (this.data.get('requestNeed')) {
            return;
        }
        const region = this.$context.getCurrentRegion().id;
        const availableRegion = getVPCSupportRegion(window);
        // 当前只开放南京、北京、苏州、武汉
        if (natType === 'private') {
            const networkSupportRegion = window.$context.SERVICE_TYPE['NETWORK'].region;
            const supportPrivateNatRegion = [
                {text: networkSupportRegion['nj'], value: 'nj'},
                {text: networkSupportRegion['bj'], value: 'bj'},
                {text: networkSupportRegion['su'], value: 'su'},
                {text: networkSupportRegion['fwh'], value: 'fwh'},
                {text: networkSupportRegion['bd'], value: 'bd'},
                {text: networkSupportRegion['gz'], value: 'gz'}
            ];
            this.data.set('datasource.region', intersectionBy(availableRegion, supportPrivateNatRegion, 'value'));
        } else {
            // region 平铺
            this.data.set('datasource.region', availableRegion);
        }
        this.enHanceNatWhite();
        this.data.set('resourceSDK', new ResourceGroupSDK(this.$http, window.$context));

        // 快手region不支持NAT预付费
        if (region === this.$context.getEnum('AllRegion').BJKS || natType === 'private') {
            this.data.shift('datasource.productType');
            this.data.set('formData.productType', PayType.POSTPAY);
        }
        await this.getNatQuery();
        if (!FLAG.NetworkNatOpt) {
            this.data.shift('datasource.productType');
            this.data.set('formData.productType', PayType.POSTPAY);
            this.data.set('hideNormalType', true);
        }
        // 虚商隐藏年付优惠
        if (FLAG.NetworkSupportXS) {
            this.data.set('formData.productType', PayType.PREPAY);
            let purchaseLength = this.data.get('datasource.purchaseLength');
            this.data.set(
                'datasource.purchaseLength',
                purchaseLength.map(item => {
                    if (item.mark) {
                        return {
                            text: item.text,
                            value: item.value
                        };
                    }
                    return item;
                })
            );
        }
        const vpcId = this.data.get('vpcId');

        this.data.set('formData.vpcId', vpcId);
        const isAccessNormalnat = window?.$storage?.get('commonWhite')?.NormalNatWhiteList || false;
        if (isAccessNormalnat) {
            this.getAvailAbleNat();
        }
        let {natSinDisable} = checker.check(rules, []);
        this.data.set('natSinDisable', natSinDisable);
    }

    async attached() {
        if (this.data.get('requestNeed')) {
            return;
        }
        // vpc没返回先校验处理
        try {
            await this.loadVpcList();
        } catch {}
        this.loadPrice();
        this.checkAll();
        this.checkNatBlackList();

        this.watch('formData.autoRenew', value => {
            this.updateAutoRenewTip();
            this.setConfigDetail();
        });
        this.watch('formData.autoRenewTimeUnit', value => {
            if (this.data.get('formData.autoRenewTime') > 3) {
                this.data.set('formData.autoRenewTime', 1);
            }
            this.updateAutoRenewTip();
            this.setConfigDetail();
            this.data.set('datasource.autoRenewTime', value === TimeType.YEAR ? Year.toArray() : Month.toArray());
        });
        this.watch('formData.autoRenewTime', value => {
            this.updateAutoRenewTip();
            this.setConfigDetail();
        });
        this.watch('formData.flavor', value => {
            this.loadPrice();
        });
        this.watch('formData.clusterMode', value => {
            this.data.set('vpcs', []);
            if (['v4', 'v6'].includes(value)) {
                // 清空普通型
                this.data.set('formData.eips', []);
                this.data.set('formData.dnatEips', []);
            } else {
                // 清空增强型
                this.data.set('formData.bindEips', []);
            }
            if (this.data.get('formData.cuNum')) {
                this.loadPrice();
            }
            // 筛选IPv6网段
            this.setNatTypeVpcs(value);
        });
        this.watch(
            'formData.cuNum',
            _.debounce(value => {
                this.loadPrice();
            }, 300)
        );
        this.watch('formData.eips', value => {
            this.ref('form') && this.ref('form').validateFields();
            this.setConfigDetail();
        });
        this.watch('formData.dnatEips', value => {
            this.ref('form') && this.ref('form').validateFields();
            this.setConfigDetail();
        });
        this.watch('formData.bindEips', value => {
            this.ref('form') && this.ref('form').validateFields();
            this.setConfigDetail();
        });
        this.watch('formData.vpcId', value => {
            this.checkVpc();
        });
        this.watch('formData.purchaseLength', value => {
            this.loadPrice();
        });
    }

    // 根据网关类型筛选对应网段的vpc
    setNatTypeVpcs(type: string) {
        const allVpcs = this.data.get('allVpc');
        let vpcs = [];
        if (type === 'v6') {
            const openedIPv6Vpcs = _.filter(allVpcs || [], vpc => !!vpc.ipv6Cidr);
            vpcs = _.map(openedIPv6Vpcs, item => {
                const {name, cidr, ipv6Cidr, vpcId} = item;
                return {text: `${name}（${cidr}）（${ipv6Cidr}）${ipv6Cidr ? '(' + ipv6Cidr + ')' : ''}`, value: vpcId};
            });
        } else {
            vpcs = _.map(allVpcs, item => {
                const {name, cidr, ipv6Cidr, vpcId} = item;
                return {text: `${name}（${cidr}）${ipv6Cidr ? '(' + ipv6Cidr + ')' : ''}`, value: vpcId};
            });
        }
        this.nextTick(() => {
            this.data.set('vpcs', vpcs);
            this.data.set('formData.vpcId', vpcs?.[0]?.value);
        });
    }

    // 获取配置的表单数据
    getConfigForm() {
        const urlQuery = this.data.get('urlQuery');
        if (urlQuery?.config) {
            const decryptConfig = decryptUrlBtoa(urlQuery.config);
            const formData = this.data.get('formData');
            this.data.set('formData', {...formData, ...decryptConfig, clusterMode: true});
            this.data.set('cmcFormData', decryptConfig);
            this.data.set('vpcId', decryptConfig?.vpcId);

            const region = decryptConfig?.region;
            if (region !== window.$context.getCurrentRegionId()) {
                window.$context.setRegion(region);
            }
        }
    }

    initPrivateNatConfig(natType: 'private' | 'public') {
        const backUrl = natType === 'private' ? '/network/#/vpc/privateNat/list' : '/network/#/vpc/nat/list';
        natType === 'private' && this.checkPrivateUserQuota();
        this.data.set('backUrl', backUrl);
        this.data.set('natType', natType);
    }
    cancel() {
        const isPrivate = this.data.get('isPrivate');
        const backUrl = isPrivate ? '#/vpc/privateNat/list' : '#/vpc/nat/list';
        location.hash = backUrl;
    }

    enHanceNatWhite() {
        let result = window.$storage.get('commonWhite') || {
            NormalNatWhiteList: false
        };
        // 增强型NAT
        this.data.push('datasource.clusterMode', {
            text: '增强型-IPv4',
            value: 'v4'
        });
        const enableV6Region = [AllRegion.SU, AllRegion.NJ, AllRegion.FWH];
        const currRegion = window.$context.getCurrentRegionId();
        const isEnableV6 = enableV6Region.includes(currRegion);
        if (isEnableV6) {
            this.data.push('datasource.clusterMode', {
                text: '增强型-IPv6',
                value: 'v6'
            });
        }

        // 普通型NAT
        if (!result.NormalNatWhiteList) {
            const datasource = [{text: '增强型-IPv4', value: 'v4'}];
            if (isEnableV6) {
                datasource.push({text: '增强型-IPv6', value: 'v6'});
            }
            this.data.set('datasource.clusterMode', datasource);
            this.data.set('formData.clusterMode', 'v4');
            this.data.set('clusterModeInit', 'v4');
        }
    }

    filterVpcList(vpcs, value) {
        // 组织项目需要先过滤当前组织项目下的列表
        if (FLAG.NetworkNatSupOrganization && value) {
            return vpcs.filter(item => {
                let resourceGroupIds = item.resourceGroups.map(resourceGroup => resourceGroup.resourceGroupId);
                return resourceGroupIds.indexOf(value) > -1;
            });
        }
        return vpcs;
    }

    setVpcId(vpcs) {
        const item = _.find(vpcs, item => !item.disabled);
        const vpcId = (item && item.value) || '';
        this.data.set('formData.vpcId', vpcId);
    }

    getInitFormData(list: any[], clusterModeInit?: unknown) {
        const region = this.$context.getCurrentRegion();
        const flavorList = list || [];
        let flavor = NatFlavor.L;
        let index = flavorList.findIndex(item => !item.sellOut);
        if (index !== -1) {
            flavor = flavorList[index].value;
        }
        // 开通页跳转到创建页，要选中购买时长为1年
        return {
            productType: PayType.PREPAY,
            region: region.id,
            name: '',
            description: '', // 增添description字段
            flavor,
            eips: [],
            dnatEips: [],
            bindEips: [], // 增强型
            purchaseLength: 1,
            autoRenew: false,
            autoRenewTimeUnit: TimeType.MONTH,
            autoRenewTime: Month.ONE,
            cuNum: 1,
            clusterMode: clusterModeInit !== undefined && clusterModeInit !== null ? clusterModeInit : false,
            tcpTimeOut: 900,
            udpTimeOut: 30,
            icmpTimeOut: 30
        };
    }

    updateAutoRenewTip() {
        const {autoRenewTimeUnit, autoRenewTime} = this.data.get('formData');
        const expireTimeText = html`系统将于到期前7天进行扣费，扣费时长为${autoRenewTime}${autoRenewTimeUnit ===
        TimeType.YEAR
            ? '年'
            : '月'}`; // eslint-disable-line
        this.data.set('autoRenewTip', expireTimeText);
    }

    loadSubnets(vpcId: string) {
        this.data.set('loading', true);
        this.data.set('formData.subnetId', '');
        this.$http
            .vpcSubnetList({vpcId, attachVm: false})
            .then((res: any) => {
                let datasource = [];
                _.each(res, item =>
                    datasource.push({
                        value: item.subnetId,
                        text: item.name + (item.cidr ? '（' + item.cidr + '）' : ''),
                        az: item.az
                    })
                );
                this.data.set('subnetDatasource', datasource);
            })
            .finally(() => this.data.set('loading', false));
    }
    loadVpcList() {
        let vpcId = this.data.get('vpcId');
        const isPrivate = this.data.get('isPrivate');
        return this.$http.vpcList().then(data => {
            let vpcs = _.map(data, item => ({
                text: `${item.name}（${item.cidr}）${item.ipv6Cidr ? '(' + item.ipv6Cidr + ')' : ''}`,
                value: item.vpcId
            }));
            this.data.set('vpcs', vpcs);
            this.data.set('allVpc', data);
            vpcId = vpcId || (vpcs && vpcs[0].value);
            this.data.set('formData.vpcId', vpcId);
            isPrivate && this.loadSubnets(vpcId);
            this.checkVpc();
        });
    }

    // 切换vpc
    vpcChange({value}) {
        const isPrivate = this.data.get('isPrivate');
        isPrivate && this.loadSubnets(value);
    }

    // 获取当前子网可用区
    handleSubnetChange(e: {value: string}) {
        const subnetId = e.value;
        const subnetDatasource = this.data.get('subnetDatasource');
        const currSubnet = subnetDatasource.find((item: any) => item.value === subnetId);
        const currZone = zone.getLabel(currSubnet.az);
        this.data.set('currZone', currZone);
        this.$http.getSubnetDetail(subnetId).then((res: any) => {
            if (res?.subnets) {
                const currResSubnets = res.subnets[0];
                const availableIPs = currResSubnets?.totalIps - currResSubnets?.usedIps || 0;
                if (+availableIPs === 0) {
                    this.data.set(
                        'noneIpTip',
                        `当前${currZone}，可用IP${0}个；如需创建子网，您可以到<a href="#/vpc/subnet/list" target="_blank"> 私有网络-子网 </a>去创建`
                    );
                } else {
                    this.data.set('noneIpTip', '');
                }
                this.data.set('availableIPs', availableIPs);
            }
        });
    }

    getEipContent(eips, type) {
        const ui = this.ref(type);
        const eipType = ui?.data.get('formData.eipType');
        let bandWidth = [];
        let datasource = [];
        if (eipType === EipType.GROUP) {
            datasource = ui?.data.get('datasource.eipGroupDetail');
        } else {
            datasource = ui?.data.get('datasource.eipList');
        }
        _.each(eips, eip => {
            const data = _.find(datasource, item => item.value === eip);
            bandWidth.push(data ? data.bandWidth : '-');
        });
        return html`${bandWidth.join('、')}（${eipType === EipType.GROUP ? '共享带宽，' : ''}${eips.length}个IP）`;
    }

    getConfig() {
        const formData = this.data.get('formData');
        const isPrivate = this.data.get('isPrivate');
        let reservation =
            formData.productType === PayType.PREPAY
                ? {reservationLength: formData.purchaseLength, reservationTimeUnit: 'MONTH'}
                : {};
        let data: any = {
            name: formData.name,
            vpcId: formData.vpcId,
            billing: {reservation, billingMethod: formData.productType},
            count: 1,
            description: formData.description,
            resourceGroupId: formData.resourceGroupId,
            clusterMode: Boolean(formData.clusterMode),
            deleteProtect: this.data.get('deleteProtect'),
            sessionConfig: {
                tcpTimeOut: formData.tcpTimeOut * 1000,
                udpTimeOut: formData.udpTimeOut * 1000,
                icmpTimeOut: formData.icmpTimeOut * 1000
            }
        };
        if (isPrivate) {
            delete data.vpcId;
            delete data.clusterMode;
            data.vpcUuid = formData.vpcId;
            data.subnetUuid = formData.subnetId;
        }
        if (formData.clusterMode || isPrivate) {
            data.cuNum = formData.cuNum;
        } else {
            data.flavor = formData.flavor;
            delete data.sessionConfig;
        }
        if (formData.productType === PayType.PREPAY && formData.autoRenew) {
            data.renewReservation = {
                reservationLength: formData.autoRenewTime,
                reservationTimeUnit: formData.autoRenewTimeUnit
            };
        }
        if (formData.bindEips.length) {
            data.bindEips = formData.bindEips;
        }
        if (formData.eips.length) {
            data.eips = formData.eips;
        }
        if (formData.dnatEips.length) {
            data.dnatEips = formData.dnatEips;
        }
        return data;
    }

    loadPrice() {
        const {newBillingSdk} = this.data.get('');
        newBillingSdk.clearItems();
        let configs = this.initPriceFlavor();
        const orderItem = new OrderItem(configs);
        this.data.set('bucketItems', [orderItem]);
        newBillingSdk.addItems([orderItem]);
    }

    checkVpc() {
        if (!this.data.get('formData.vpcId') || this.data.get('unablePay')) {
            return;
        }
        this.checkQuota().then(result => {
            if (!result) {
                if (FLAG.NetworkSupportXS) {
                    this.data.set('bucket.tip', '当前所在网络nat网关配额不足');
                } else {
                    const isPrivate = this.data.get('isPrivate');
                    const quotaCenterName = isPrivate ? 'IntranetNatVpcQuota' : 'natQuota';
                    this.data.set(
                        'bucket.tip',
                        `当前所在网络nat网关配额不足，<a href="/quota_center/#/quota/apply/create?serviceType=NAT&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=${quotaCenterName}" target="_blank">去申请配额</a>`
                    );
                }
                this.data.set('bucket.disabled', true);
            } else {
                this.data.set('bucket.tip', '');
                this.data.set('bucket.disabled', false);
            }
            this.setBucketDisabled();
        });
    }

    checkAll() {
        const isVerifyUser = this.$context.isVerifyUser();
        if (!isVerifyUser) {
            this.data.set('unablePay', true);
            this.data.set('bucket.disabled', true);
            if (FLAG.NetworkSupportXS) {
                this.data.set('bucket.tip', '温馨提示：您还没有实名认证，请先完成实名认证');
            } else {
                this.data.set(
                    'bucket.tip',
                    '温馨提示：您还没有实名认证，请立即去<a href="/qualify/#/qualify/index">认证</a>'
                );
            }
            return;
        }
        this.checkAccountPurchaseValidation().then(result => {
            if (!result.status) {
                this.data.set('unablePay', true);
                let reason =
                    result.failReason + `请及时<a href="/finance/#/finance/account/recharge" target="_blank">充值</a>`; // eslint-disable-line
                this.data.set('bucket.tip', reason);
                this.data.set('bucket.disabled', true);
            }
            this.setBucketDisabled();
        });
    }

    checkAccountPurchaseValidation() {
        const formData = this.data.get('formData');
        return this.$http.purchaseValidation({
            productType: formData.productType,
            serviceType: 'NAT'
        });
    }

    checkQuota() {
        const isPrivate = this.data.get('isPrivate');
        const natType = this.data.get('formData.clusterMode');
        const isEnhancedIPv6 = natType === 'v6';
        const quotaUrl = isPrivate ? 'getVpcPrivateNatQuota' : isEnhancedIPv6 ? 'getVpcIPv6NatQuota' : 'getNatQuota';
        return this.$http[quotaUrl]({
            id: this.data.get('formData.vpcId')
        })
            .then(data => data.free > 0)
            .catch(() => false);
    }
    // 私网NAT用户配额
    checkPrivateUserQuota() {
        this.$http.getUserPrivateNatQuota().then(res => {
            const {free} = res;
            if (free <= 0) {
                if (FLAG.NetworkSupportXS) {
                    this.data.set('bucket.tip', '当前用户nat网关配额不足');
                } else {
                    const quotaCenterName = 'IntranetNatUserQuota';
                    this.data.set(
                        'bucket.tip',
                        `当前用户nat网关配额不足，请<a href="/quota_center/#/quota/apply/create?serviceType=NAT&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=${quotaCenterName}" target="_blank">申请配额</a>`
                    ); // eslint-disable-line
                    this.data.set('bucket.disabled', true);
                }
            }
        });
    }

    onConfirm() {
        const form = this.ref('form');
        return form.validateFields().then(async () => {
            try {
                await this.ref('tagPanel').validate(false);
            } catch (error) {
                return;
            }
            const formData = this.data.get('formData');
            const isPrivate = this.data.get('isPrivate');
            let natType = '';
            const {flavor, clusterMode} = formData;
            if (clusterMode) {
                natType = NAT_ENHANCED_MAPPING.getTextFromValue(clusterMode);
            } else {
                const config = NatFlavor.fromValue(flavor);
                natType = config.text ? `普通型（${config.text}）` : '普通型（超大）';
            }
            let configDetail = [
                {
                    label: '地域',
                    value: this.$context.getCurrentRegion().label
                },
                {
                    label: 'NAT名称',
                    value: formData.name || '-'
                },
                {
                    label: '类型',
                    value: natType
                }
            ];
            if (formData.eips && formData.eips.length > 0) {
                configDetail.push({
                    label: 'SNAT公网IP',
                    value: this.getEipContent(formData.eips, 'snat')
                });
            }
            if (formData.dnatEips && formData.dnatEips.length > 0) {
                configDetail.push({
                    label: 'DNAT公网IP',
                    value: this.getEipContent(formData.dnatEips, 'dnat')
                });
            }
            if (formData?.bindEips?.length) {
                configDetail.push({
                    label: '公网IP',
                    value: this.getEipContent(formData.bindEips, 'bindEips')
                });
            }
            if (isPrivate) {
                configDetail.splice(2, 1);
                configDetail.push({
                    label: '性能容量',
                    value: `${formData.cuNum}CU`
                });
            }
            const orderItem = this.data.get('bucketItems');
            orderItem[0].updateConfigDetail(configDetail);
            let tagData = await this.ref('tagPanel').getTags();
            this.data.set('tagData', tagData || {});
            this.data.set('confirming', true);
            this.data.set('currentSteps', 2);
        });
    }

    onBack() {
        this.data.set('currentSteps', 1);
        this.data.set('confirming', false);
        this.setConfigDetail();
    }

    async addShoppingCart() {
        let form = this.ref('form');
        await form.validateFields();
        this.data.set('cartConfirming', true);
        this.loadPrice().then(() => {
            let config = this.getConfig();
            this.$http
                .addNatShoppingCart({
                    paymentMethod: [],
                    items: [
                        {
                            config: config,
                            paymentMethod: []
                        }
                    ]
                })
                .then(result => {
                    this.data.set('cartConfirming', false);
                    window.shoppingCart?.showSuccessTip();
                    window.shoppingCart?.refreshCount();
                })
                .catch(result => {
                    this.data.set('cartConfirming', false);
                });
        });
    }

    async onPay() {
        !FLAG.NetworkSupportXS && (await this.ref('billingProtocol').validateAgreement());
        this.data.set('disableSub', true);
        const extraConfig = {};
        let {newBillingSdk, bucketItems} = this.data.get('');
        const isPrivate = this.data.get('isPrivate');
        if (FLAG.NetworkNatSupOrganization) {
            const projectValid = this.ref('projectConfig').validComponentData();
            if (!projectValid) {
                return;
            }
            extraConfig.resourceGroupIds = [this.data.get('resourceGroupId')];
        }
        let params = {
            items: [
                {
                    config: {
                        ...this.getConfig(),
                        ...extraConfig,
                        tags: this.data.get('tagData')
                    },
                    paymentMethod: bucketItems[0].couponId ? [{type: 'coupon', values: [bucketItems[0].couponId]}] : []
                }
            ]
        };
        try {
            const isEnhancedIPv6 = this.data.get('isEnhancedIPv6');
            // 后端私网、IPv4、IPv6各搞了一套
            let confirmUrl = '/api/nat/order/confirm/new?orderType=NEW';
            if (isPrivate) {
                confirmUrl = '/api/intranet/nat/order/confirm/new?orderType=NEW';
            } else if (isEnhancedIPv6) {
                confirmUrl = '/api/nat/ipv6/order/confirm/new?orderType=NEW';
                params.items[0].config.vpcUuid = params.items[0].config.vpcId;
                delete params.items[0].config.vpcId;
            }
            const data = await this.$http.newConfirmOrder(confirmUrl, params);

            // 和迁移平台资源建立映射关系
            const cmcFormData = this.data.get('cmcFormData');
            if (cmcFormData && cmcFormData?.sourceResourceId) {
                const orderId = data?.orderId;
                const payload = {
                    sourceCloud: cmcFormData?.sourceCloud,
                    sourceRegion: cmcFormData?.sourceRegion,
                    sourceResourceId: cmcFormData?.sourceResourceId,
                    targetResourceOrder: orderId,
                    status: 'migrating'
                };
                const resourceList = [payload];
                await updateResource({resourceList});
            }

            let url = '';
            try {
                const info = await newBillingSdk.checkPayInfo(data);
                url = info.url + '&fromService=NAT';
                info.url && (location.href = url);
            } catch (info) {
                // 跳转到相应页面
                url = info.url + '&fromService=NAT';
                info.url && (location.href = url);
            }
        } catch (err) {}
        this.data.set('disableSub', false);
    }

    onReset() {
        this.ref('form').resetFields();
        const flavorList = this.data.get('flavorList');
        const clusterModeInit = this.data.get('clusterModeInit');
        let newFormData = _.extend({}, this.data.get('formData'), this.getInitFormData(flavorList, clusterModeInit));

        this.ref('snat').initLoadData('normal');
        this.ref('dnat').initLoadData('normal');

        this.data.set('formData', newFormData);
        this.nextTick(() => {
            this.data.set('formData.flavor', this.getInitFormData(flavorList, clusterModeInit).flavor);
        });
    }

    checkNatBlackList() {
        return this.$http.natBlackList().then(data => this.data.set('natBlackList', data));
    }

    onNatChange(type, value) {
        this.data.set(`formData.${type}`, value);
    }
    getNatQuery() {
        return this.$http
            .iamStsRoleQuery(
                {
                    roleName: natRoleName
                },
                {region: AllRegion.BJ}
            )
            .then(data => {
                if (!data || (data && !data.name)) {
                    return this.$http.iamStsRoleActivate(
                        _.extend(
                            {
                                roleName: natRoleName,
                                accountId: getUserId()
                            },
                            isOnline() ? StsConfig.NAT.online : StsConfig.NAT.sandbox
                        ),
                        {region: AllRegion.BJ}
                    );
                }
                return Promise.resolve();
            });
    }
    resourceChange({value}: {value: any}) {
        this.data.set('formData.resourceGroupId', value.data.groupId);
    }
    getAvailAbleNat() {
        let typeArray = NatFlavor.toArray().map(item => item.value);
        let promiseArray = [];
        for (let type of typeArray) {
            promiseArray.push(this.$http.getAvailAbleNat({type}).catch(e => Promise.resolve({})));
        }

        Promise.all(promiseArray)
            .then(res => {
                let flavorList = [];
                typeArray.forEach((item, index) => {
                    flavorList.push({
                        sellOut: res[index].availableNumber === 0,
                        value: item,
                        label: NatFlavor.getTextFromValue(item)
                    });
                });
                flavorList.push({
                    sellOut: false,
                    value: 'enhanced_12c6q',
                    label: '超大'
                });
                this.data.set('flavorList', flavorList);
                let index = flavorList.findIndex(item => !item.sellOut);
                if (index !== -1) {
                    this.data.set('formData.flavor', flavorList[index].value);
                } else {
                    // 保证询价不报错的同时禁止购买
                    this.data.set('formData.flavor', flavorList[0].value);
                    this.data.set('sellOutDisabled', true);
                }
            })
            .catch(error => {
                this.data.set('flavorList', [
                    {sellOut: false, value: 'little', label: '小'},
                    {sellOut: false, value: 'medium', label: '中'},
                    {sellOut: false, value: 'large', label: '大'},
                    {sellOut: false, value: 'enhanced_12c6q', label: '超大'}
                ]);
                this.data.set('formData.flavor', 'little');
            });
    }
    setBucketDisabled() {
        if (this.data.get('bucket.disabled') || this.data.get('unablePay')) {
            return;
        }
        if (this.data.get('sellOutDisabled')) {
            this.data.set('unablePay', true);
            this.data.set('bucket.tip', '售罄！请您移步其他地域购买资源。');
            this.data.set('bucket.disabled', true);
        } else {
            this.data.set('unablePay', false);
            this.data.set('bucket.disabled', false);
        }
    }
    initPriceFlavor() {
        const payload = this.getConfig();
        const isPrivate = this.data.get('isPrivate');
        let priceParams: Record<string, any> = {
            serviceType: 'NAT',
            configName: '配置费用',
            serviceName: 'NAT网关',
            chargeItem: payload.billing.billingMethod === 'prepay' ? 'Cpt2' : 'RunningTimeMinutes',
            productType: payload.billing.billingMethod,
            region: window.$context.getCurrentRegionId(),
            scene: 'NEW',
            timeUnit: payload.billing.billingMethod === 'prepay' ? 'MONTH' : 'MINUTE',
            count: 1,
            duration: payload.billing.reservation.reservationLength,
            flavor: this.getFlavorData(payload)
        };
        if (isPrivate) {
            delete priceParams.duration;
            priceParams.chargeItemName = 'RunningTimeMinutes';
            priceParams.subServiceType = 'default';
            priceParams.billingQuery = false;
        }
        // 新增shoppingCart详情展示页面
        let configuration = this.getConfigDetail();
        priceParams.configDetail = configuration;

        return priceParams;
    }
    getFlavorData(payload) {
        // 公网NAT
        let flavorList = [{name: 'nat_gateway_scale', value: payload.flavor, scale: 1}];
        if (payload.clusterMode) {
            flavorList = [
                {name: 'subServiceType', value: 'enhanced', scale: 1},
                {name: 'Instance', value: 'default', scale: 1},
                {name: 'CU', value: payload.cuNum, scale: 1}
            ];
        }
        // 私网NAT
        const isPrivate = this.data.get('isPrivate');
        const privateFlavor = [
            {
                name: 'Instance',
                value: 'default',
                scale: 1
            },
            {
                name: 'CU',
                value: payload.cuNum,
                scale: 1
            },
            {
                name: 'subServiceType',
                value: 'default',
                scale: 1
            }
        ];
        return isPrivate ? privateFlavor : flavorList;
    }
    productTypeChange(value) {
        this.data.set('formData.productType', value);
        this.loadPrice();
        this.checkAll();
    }
    // 新增onShoppingCartChange
    onShoppingCartChange(e) {
        this.data.set('priceLoading', UPDATE_STATUS[e] !== UPDATE_STATUS.DONE);
        if (UPDATE_STATUS[e] === UPDATE_STATUS.DONE) {
            const bucketItems = this.data.get('bucketItems');
            const {newBillingSdk, formData} = this.data.get('');
            let unitPrice = bucketItems[0]?.unitPrice;
            let priceSubText = bucketItems[0]?.priceSubText;
            // 用来判断是否需要二次询价
            if (formData.productType !== PayType.PREPAY && unitPrice && !priceSubText) {
                newBillingSdk.clearItems();
                let extra =
                    '预计' + showMoney(unitPrice * 60 * 24) + '/天，' + showMoney(unitPrice * 60 * 24 * 30) + '/月'; // eslint-disable-line
                bucketItems[0].priceSubText = extra;
                this.data.set('bucketItems', bucketItems);
                newBillingSdk.addItems(bucketItems);
            }
        }
    }

    getConfigDetail() {
        let natType = '';
        const formData = this.data.get('formData');
        const isPrivate = this.data.get('isPrivate');
        const {flavor, clusterMode} = formData;
        if (clusterMode) {
            natType = NAT_ENHANCED_MAPPING.getTextFromValue(clusterMode);
        } else {
            const config = NatFlavor.fromValue(flavor);
            natType = config.text ? `普通型（${config.text}）` : '普通型（超大）';
        }

        let text = html`NAT网关（1个）${isPrivate ? '' : `；类型：${natType}`}`;
        if (formData.eips && formData.eips.length > 0) {
            text += html`；SNAT公网IP：${this.getEipContent(formData.eips, 'snat')}`;
        }
        if (formData.dnatEips && formData.dnatEips.length > 0) {
            text += html`；DNAT公网IP：${this.getEipContent(formData.dnatEips, 'dnat')}`;
        }
        if (formData?.bindEips?.length) {
            text += html`；公网IP：${this.getEipContent(formData.bindEips, 'bindEips')}`;
        }
        let config = [formData.productType === PayType.PREPAY ? `1个 * ${formData.purchaseLength}个月` : '1个'];
        formData.productType === PayType.PREPAY &&
            formData.autoRenew &&
            (config[1] =
                '开通自动续费' +
                formData.autoRenewTime +
                (formData.autoRenewTimeUnit === TimeType.YEAR ? '年' : '个月'));
        let datasource = [
            {label: '地域', value: this.$context.getCurrentRegion().label},
            {label: '配置', value: text},
            {label: '配额', value: config}
        ];
        if (isPrivate) {
            datasource.push({label: '性能容量', value: `${formData.cuNum} CU`});
        }
        return datasource;
    }
    setConfigDetail() {
        let configuration = this.getConfigDetail();
        const orderItem = this.data.get('bucketItems');
        if (!orderItem[0]) {
            return;
        }
        orderItem[0].updateConfigDetail(configuration);
    }
    // 切换地域
    onRegionChange(e) {
        let value = e.value || e.id;
        if (!value || value === window.$context.getCurrentRegionId()) {
            return;
        }
        this.data.set('formData.region', value);
        window.$context.setRegion(value);
        this.data.set('loadNeed', true);
    }
    tagListRequster() {
        return this.$http.getSearchTagList({serviceType: [], region: ['global']});
    }
    showAssist(type: string) {
        Assist.sendMessageToAssist({
            sceneLabel: 'nat_create',
            message:
                type === 'flavor'
                    ? 'NAT网关的性能容量对网络连接有哪些影响？'
                    : type === 'region'
                      ? '什么是地域？'
                      : '自动续费'
        });
    }
    showConfigure() {
        let showAdvance = this.data.get('showAdvance');
        this.data.set('showAdvance', !showAdvance);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatCreate));
