/*
 * @description: 路由表创建
 * @file: route/pages/create.js
 * @author: <EMAIL>
 */
import u from 'lodash';
import {Component} from 'san';
import {html, decorators, Processor} from '@baiducloud/runtime';
import {checker} from '@baiducloud/bce-opt-checker';
import {Notification, Select, Dialog, Tooltip} from '@baidu/sui';
import {TipRadio, Tip} from '@baidu/sui-biz';
import {OutlinedPlus} from '@baidu/sui-icon';
import {DocService} from '@/pages/sanPages/common';
import {
    RouteType,
    IpTypeVersion,
    PathType,
    NatStatus,
    VpnStatus,
    PeerConnStatus,
    DcGatewayStatus,
    EniStatus
} from '@/pages/sanPages/common/enum'; // eslint-disable-line
import {disable_vpn_region} from '@/pages/sanPages/common/flag';
import RULE from '@/pages/sanPages/utils/rule';
import {serviceTypeUrl, checkSts} from '@/pages/sanPages/utils/config';
import {checkIpv6Cidr, convertCidrToBinary, checkIsInSubnet} from '@/pages/sanPages/utils/common';
import CustomTable from '@/pages/sanPages/pages/route/page/detail/routeRule/customTable';

import checkRules from '../../../rules';
import '../../../style/create.less';

const AllRegion = window.$context.getEnum('AllRegion');
const {asComponent, invokeSUI, invokeSUIBIZ, template, invokeBceSanUI, invokeComp} = decorators;
const kXhrOptions = {'X-silence': true};
const nextHopDcMap = {
    ha: [
        {
            name: '主路径',
            nexthopId: '',
            nexthopType: RouteType.GW,
            pathType: PathType.ACTIVE
        },
        {
            name: '备路径',
            nexthopId: '',
            nexthopType: RouteType.GW,
            pathType: PathType.STANDBY
        }
    ],
    ecmp: [
        {
            name: '路径1',
            nexthopId: '',
            nexthopType: RouteType.GW,
            pathType: PathType.ECMP
        },
        {
            name: '路径2',
            nexthopId: '',
            nexthopType: RouteType.GW,
            pathType: PathType.ECMP
        }
    ]
};
const tpl = html`
    <template>
        <s-dialog
            class="route-dialog-wrap"
            width="500"
            open="{=open=}"
            title="{{title}}"
            closeAfterMaskClick="{{false}}"
        >
            <s-form s-ref="form" class="route-create-wrap" data="{=formData=}" rules="{{rules}}" label-align="left">
                <s-form-item prop="name" label="网段类型：">
                    <s-tipradio-group
                        class="create-radio-wrap"
                        on-change="ipTypeChange"
                        value="{=formData.ipType=}"
                        radioType="button"
                        name="ipType"
                    >
                        <s-tipradio
                            isDisabledVisibile="{{true}}"
                            disabled="{{editRule || v4Disable.disable || loadSource}}"
                            label="IPv4"
                            value="4"
                        >
                            <!--bca-disable-next-line-->
                            <div slot="content">{{v4Disable.message || '当前不可更改网段类型' | raw}}</div>
                        </s-tipradio>
                        <s-tipradio
                            isDisabledVisibile="{{true}}"
                            disabled="{{editRule || v6Disable.disable || loadSource}}"
                            label="IPv6"
                            value="6"
                        >
                            <!--bca-disable-next-line-->
                            <div slot="content">{{v6Disable.message || '当前不可更改网段类型' | raw}}</div>
                        </s-tipradio>
                    </s-tipradio-group>
                </s-form-item>
                <s-form-item class="require-wrap" prop="sourceAddress" label="源网段：">
                    <ui-select
                        width="{{324}}"
                        value="{=formData.sourceAddress=}"
                        datasource="{{sourceAddressList}}"
                        on-change="sourceAddressChange"
                    />
                    <!--bca-disable-next-line-->
                    <p s-if="sourceValidateTip">{{sourceValidateTip | raw}}</p>
                    <s-form-item
                        class="custom-wrap require-wrap"
                        prop="customSourceAddress"
                        s-if="formData.sourceAddress === 'CUSTOM'"
                    >
                        <s-input
                            placeholder="请输入源网段，如{{formData.ipType == 4 ? '***********/16' : 'f1::1/128'}}"
                            value="{=formData.customSourceAddress=}"
                            width="{{324}}"
                        >
                        </s-input>
                        <!--bca-disable-next-line-->
                        <p s-if="customValidateTip">{{customValidateTip | raw}}</p>
                    </s-form-item>
                </s-form-item>
                <s-form-item class="require-wrap dest_class" prop="destinationAddress" label="目标网段：">
                    <s-input
                        placeholder="请输入目的网段，如{{formData.ipType == 4 ? '10.0.0.0/16' : 'f2::2/128'}}"
                        width="{{324}}"
                        value="{=formData.destinationAddress=}"
                        on-input="inputChange"
                    />
                    <p s-if="showTip" class="showTip">
                        如果访问私网连接（如IDC或对等连接），推荐使用明细路由，否则网络不通。
                    </p>
                    <!--bca-disable-next-line-->
                    <p s-if="destinationValidateTip">{{destinationValidateTip | raw}}</p>
                </s-form-item>
                <s-form-item prop="routeType" label="路由类型：">
                    <s-select
                        disabled="{{editRule}}"
                        width="{{150}}"
                        value="{=formData.routeType=}"
                        on-change="routeTypeChange"
                    >
                        <s-select-option
                            s-for="item in routeTypeList"
                            value="{{item.value}}"
                            disabled="{{item.disabled}}"
                            label="{{item.text}}"
                        >
                            <s-tooltip trigger="{{item.disabled ? 'hover' : ''}}" placement="right" layerWidth="300">
                                <div slot="content">
                                    <!--bca-disable-next-line-->
                                    {{item.value | noOpenTip | raw}}
                                </div>
                                <div>{{item.text}}</div>
                            </s-tooltip>
                        </s-select-option>
                    </s-select>
                    <div class="radio-tip-wrap" s-if="formData.routeType === 'dcGateway'">
                        <s-radio-radio-group
                            enhanced
                            class="create-radio-wrap route-class-type"
                            on-change="dcgwTypeChange"
                            value="{=formData.dcgwType=}"
                            radioType="button"
                        >
                            <s-radio label="单线路由" disabled="{{!canChange}}" value="single" />
                            <s-radio label="多线路由" disabled="{{!canChange}}" value="multi" />
                        </s-radio-radio-group>
                        <s-tip class="radio-tip" useNewVersion layerWidth="200" content="{{radioTip}}" />
                    </div>
                    <p class="route-multi-tip" s-if="isMulti" s-html="multiRouteTipText"></p>
                </s-form-item>
                <s-form-item s-if="isMulti" label="多线模式：">
                    <s-radio-radio-group
                        disabled="{{editRule}}"
                        class="create-radio-wrap route-class-type"
                        on-change="multiTypeChange"
                        value="{=formData.multiType=}"
                    >
                        <s-radio disabled="{{editRule}}" label="主备" value="ha" />
                        <s-popover
                            class="wrap-pop"
                            trigger="{{noneMultiType.disable || ipv6NoneMultiType.disable ? 'hover' : ''}}"
                            s-if="isMulti"
                        >
                            <div slot="content">{{noneMultiType.message || ipv6NoneMultiType.message}}</div>
                            <s-radio label="负载均衡" value="ecmp" disabled="{{editRule || noneMultiType.disable}}" />
                        </s-popover>
                    </s-radio-radio-group>
                </s-form-item>
                <s-form-item class="require-wrap" s-if="isMulti" label="下一跳实例：">
                    <div s-for="item, index in nextHopDc">
                        <div class="nexthop-item-wrap">
                            <span class="nexthop-name">{{item.name}}</span>
                            <ui-select
                                s-if="{{item.name === '备路径'}}"
                                width="{{259}}"
                                filter
                                disabled="{{item.disabled}}"
                                value="{=item.nexthopId=}"
                                datasource="{{dcgwSelectDataSource}}"
                            />
                            <ui-select
                                s-else
                                width="{{259}}"
                                filter
                                disabled="{{item.disabled}}"
                                value="{=item.nexthopId=}"
                                datasource="{{nextHopList}}"
                            />
                            <s-icon
                                class="delete-icon"
                                s-if="(index > 1 || editRule) && !dcgwMultiDisable"
                                name="x"
                                on-click="deleteRoutePath(item, index)"
                            ></s-icon>
                        </div>
                    </div>
                    <div class="nexthop-err" s-if="nextHopDcErr!== ''">{{nextHopDcErr}}</div>
                    <s-button
                        style="padding:0"
                        s-if="formData.multiType === 'ecmp'"
                        disabled="{{nextHopDc.length >= multiRouteQuota || dcgwMultiDisable ||
                    (nextHopDc.length > 3 && formData.ipType == 6)}}"
                        skin="stringfy"
                        on-click="addPath"
                    >
                        <outlined-plus />
                        添加路径
                    </s-button>
                    <div class="multi-check-box" s-if="showMultiPath">
                        主路径抢占:
                        <s-popover
                            content="VPN为备路径模式，不支持主路径抢占"
                            trigger="{{preemptiveDisabled ? 'hover' : 'null'}}"
                        >
                            <s-switch checked="{=formData.preemptiveMode=}" disabled="{{preemptiveDisabled}}" />
                        </s-popover>
                        <s-tip
                            class="inline-tip main-path-tip"
                            content="{{'开启后，用户配置的主路径在健康状态下始终为主。'}}"
                            skin="question"
                        />
                    </div>
                </s-form-item>
                <s-form-item
                    s-elif="{{!isMulti && formData.routeType !== 'custom'}}"
                    class="require-wrap"
                    prop="nexthopId"
                    label="下一跳实例："
                >
                    <s-select class="nexthop-select" width="{{324}}" filterable value="{=formData.nexthopId=}">
                        <s-select-option s-for="item in nextHopList" value="{{item.value}}" label="{{item.text}}">
                            <s-tooltip>
                                <div slot="content">{{item.text}}</div>
                                <div>{{item.text}}</div>
                            </s-tooltip>
                        </s-select-option>
                    </s-select>
                </s-form-item>
                <s-form-item
                    s-elif="{{!isMulti &&  (editRule ? editRule.nexthopType === 'custom' : formData.routeType === 'custom')}}"
                    class="require-wrap custom-table"
                    label="下一跳实例："
                >
                    <custom-table
                        nexthopId="{{editRule.nexthopId}}"
                        vpcUuid="{{vpcId}}"
                        customList="{{nextHopList}}"
                        on-customSelect="customSelect"
                    />
                </s-form-item>
                <s-form-item prop="description" label="描述：">
                    <s-input width="{{324}}" value="{=formData.description=}" />
                </s-form-item>
            </s-form>
            <div slot="footer">
                <s-tip-button
                    disabled="{{confirmDisabled}}"
                    skin="primary"
                    content="{{confirmDisabledTip}}"
                    isDisabledVisibile="{{true}}"
                    on-click="onConfirm"
                >
                    {{'确定'}}
                </s-tip-button>
                <!--<s-button disabled="{{updating}}" skin="primary" on-click="onConfirm">确定</s-button>-->
                <s-button on-click="closeDialog">取消</s-button>
            </div>
        </s-dialog>
    </template>
`;

@template(tpl)
@invokeSUI
@invokeSUIBIZ
@invokeBceSanUI
class RouteCreate extends Component {
    static components = {
        's-select': Select,
        's-dialog': Dialog,
        'outlined-plus': OutlinedPlus,
        's-tipradio': TipRadio,
        's-tipradio-group': TipRadio.RadioGroup,
        's-tooltip': Tooltip,
        's-tip': Tip,
        'custom-table': CustomTable
    };
    initData() {
        return {
            open: true,
            formErrors: {},
            formData: {
                routeType: 'custom',
                ipType: '4',
                dcgwType: 'single',
                multiType: 'ha',
                preemptiveMode: false
            },
            nextHopDc: nextHopDcMap.ha,
            rules: {
                sourceAddress: [
                    {required: true, message: '不能为空'},
                    {
                        validator: (rule, value, callback) => {
                            value = value || this.data.get('formData.sourceAddress');
                            const destinationAddress = this.data.get('formData.destinationAddress');
                            // if (!value) {
                            //     return callback('不能为空');
                            // }
                            if (value.startsWith('0.0.0.0') && destinationAddress.startsWith('0.0.0.0')) {
                                return callback('源网段与目标网段不能同时以0.0.0.0开头');
                            }
                            return callback();
                        }
                    }
                ],
                customSourceAddress: [
                    {required: true, message: '不能为空'},
                    {
                        validator: (rule, value, callback) => {
                            value = value || this.data.get('formData.customSourceAddress');
                            let ipType = this.data.get('formData.ipType');
                            let valueMask = value.split('/')[1];
                            // if (!value) {
                            //     return callback('不能为空');
                            // }
                            if (this.checkSourceIP(value) && !valueMask) {
                                value = value + (ipType === IpTypeVersion.IPV4 ? '/32' : '/128');
                            }
                            if (!this.checkSourcePattern(value)) {
                                return callback('CIDR格式不合法');
                            }
                            let checkCidr = this.checkSourceCidr(value);
                            if (checkCidr !== '') {
                                return callback(checkCidr);
                            }
                            return callback();
                        }
                    }
                ],
                destinationAddress: [
                    {required: true, message: '不能为空'},
                    {
                        validator: (rule, value, callback) => {
                            value = value || this.data.get('formData.destinationAddress');
                            const sourceAddress =
                                this.data.get('formData.sourceAddress') === 'CUSTOM'
                                    ? this.data.get('formData.customSourceAddress')
                                    : this.data.get('formData.sourceAddress');
                            // if (!value) {
                            //     return callback('不能为空');
                            // }
                            let type = this.data.get('formData.ipType');
                            let checkCidr = this.checkRepeatTarget();
                            if (checkCidr !== '') {
                                return callback(checkCidr);
                            }
                            if (type === IpTypeVersion.IPV4) {
                                let pattern = new RegExp(RULE.IP_CIDR);
                                if (!pattern.test(value)) {
                                    return callback('格式不正确');
                                }
                                if (!this.checkTargetCidr(value)) {
                                    return callback('CIDR格式不合法');
                                }
                            } else if (type === IpTypeVersion.IPV6 && !checkIpv6Cidr(value)) {
                                return callback('CIDR格式不合法');
                            }
                            if (value.startsWith('0.0.0.0') && sourceAddress.startsWith('0.0.0.0')) {
                                return callback('源网段与目标网段不能同时以0.0.0.0开头');
                            }
                            return callback();
                        }
                    }
                ],
                routeType: [
                    {
                        validator: (rule, value, callback) => {
                            let vpcInfo = this.data.get('vpcInfo');
                            if (value === 'TGW' && !vpcInfo.csnId) {
                                return callback('该实例暂未关联至云智能网CSN实例，请关联后再进行操作');
                            }
                            return callback();
                        }
                    }
                ],
                nexthopId: [
                    {
                        validator: (rule, value, callback) => {
                            value = value || this.data.get('formData.nexthopId');
                            if (!value) {
                                return callback('请选择');
                            }
                            return callback();
                        }
                    }
                ]
            },
            editRule: '',
            nextHopDcErr: '',
            operate: [],
            routeTypeList: RouteType.getIpv4Type(),
            canChange: true,
            showTip: false,
            nextHopList: [],
            vpnIpsecList: [],
            tgwInstanceList: [],
            preemptiveDisabled: false,
            v4Disable: {
                disable: false,
                message: ''
            },
            v6Disable: {
                disable: false,
                message: ''
            },
            loadSource: false,
            editRouteType: '',
            sourceValidateTip: '',
            radioTip:
                '专线网关单线路由切换到多线路由负载时，需分两步操作：先切路由类型并点确定，然后对当前路由条目点编辑然后增加下一跳路径。',
            hasSubnets: true // 本地网关必须需要有子网
        };
    }

    static computed = {
        title() {
            let edit = this.data.get('editRule');
            return edit ? '编辑路由' : '添加路由';
        },
        isGw() {
            let itemData = this.data.get('editRule');
            if (
                itemData &&
                itemData.nexthopType &&
                itemData.nexthopType === RouteType.GW &&
                itemData.pathType === PathType.ECMP
            ) {
                return true;
            }
            return false;
        },
        isMulti() {
            let formData = this.data.get('formData');
            return formData.routeType === 'dcGateway' && formData.dcgwType === 'multi';
        },
        updating() {
            let sourceLoad = this.data.get('sourceLoad');
            let confirmed = this.data.get('confirmed');
            let routeType = this.data.get('formData.routeType');
            let vpcInfo = this.data.get('vpcInfo');
            return sourceLoad || confirmed || (routeType === 'TGW' && !vpcInfo.csnId);
        },
        showMultiPath() {
            let formData = this.data.get('formData');
            return formData.routeType === 'dcGateway' && formData.dcgwType === 'multi' && formData.multiType === 'ha';
        },
        dcgwSelectDataSource() {
            let formData = this.data.get('formData');
            let nextHopList = this.data.get('nextHopList') || [];
            let vpnIpsecList = this.data.get('vpnIpsecList');
            if (formData.ipType === IpTypeVersion.IPV4) {
                return nextHopList.concat(vpnIpsecList);
            }
            return nextHopList;
        },
        confirmDisabled() {
            let ecmpQuota = this.data.get('ecmpQuota');
            let ecmpIpv6Quota = this.data.get('ecmpIpv6Quota');
            let formData = this.data.get('formData');
            if (
                this.data.get('updating') ||
                (formData.ipType == 4 && formData.multiType === 'ecmp' && ecmpQuota <= 0) ||
                (formData.ipType == 6 && formData.multiType === 'ecmp' && ecmpIpv6Quota <= 0)
            ) {
                return true;
            }
            return false;
        },
        confirmDisabledTip() {
            let ecmpQuota = this.data.get('ecmpQuota');
            let ecmpIpv6Quota = this.data.get('ecmpIpv6Quota');
            let formData = this.data.get('formData');
            if (formData.ipType == 4 && formData.multiType === 'ecmp' && ecmpQuota <= 0) {
                return 'IPv4 ecmp路由条目配额不足';
            } else if (formData.ipType == 6 && formData.multiType === 'ecmp' && ecmpIpv6Quota <= 0) {
                return 'IPv6 ecmp路由条目配额不足';
            }
            return '数据更新中';
        },
        customValidateTip() {
            const editRule = this.data.get('editRule');
            const allRules = this.data.get('routeRules');
            const formData = u.cloneDeep(this.data.get('formData'));
            formData.customSourceAddress = u.trim(formData.customSourceAddress);
            const customSourceAddressSplit = formData.customSourceAddress?.split('/');
            const netSegment = customSourceAddressSplit[0];
            const mask = customSourceAddressSplit[1];
            const existOriginItem = u.find(allRules, item => {
                const existNetSegment = item.sourceAddress?.split('/')[0];
                return netSegment === existNetSegment;
            });
            const existOriginItemMask = existOriginItem?.sourceAddress.split('/')[1];
            if (
                existOriginItem &&
                editRule?.sourceAddress !== formData.customSourceAddress &&
                existOriginItemMask !== mask
            ) {
                return `该源网段与已有源网段${existOriginItem.sourceAddress}重叠，请关注路由优先级 <a href=${DocService.route_rule}>路由优先级说明</a>`;
            }
            return '';
        },
        destinationValidateTip() {
            const editRule = this.data.get('editRule');
            const allRules = this.data.get('routeRules');
            const formData = u.cloneDeep(this.data.get('formData'));
            formData.destinationAddress = u.trim(formData.destinationAddress);
            const destinationAddressSplit = formData.destinationAddress?.split('/');
            const netSegment = destinationAddressSplit[0];
            const mask = destinationAddressSplit[1];
            const existOriginItem = u.find(allRules, item => {
                const existNetSegment = item.destinationAddress?.split('/')[0];
                return netSegment === existNetSegment;
            });
            const existOriginItemMask = existOriginItem?.sourceAddress.split('/')[1];
            if (
                existOriginItem &&
                editRule?.destinationAddress !== formData.destinationAddress &&
                existOriginItemMask !== mask
            ) {
                return `该目标网段与已有目标网段${existOriginItem.destinationAddress}重叠，请关注路由优先级 <a href=${DocService.route_rule}>路由优先级说明</a>`;
            }
            return '';
        },
        multiRouteTipText() {
            return `多线路由推荐下一跳实例使用专线网关的<a target="_blank" href="https://cloud.baidu.com/doc/ET/s/zjwvyvlxx#%E6%AD%A5%E9%AA%A45%EF%BC%9A%E5%88%9B%E5%BB%BA%E9%93%BE%E8%B7%AF%E6%8E%A2%E6%B5%8B">链路探测</a>，否则当专线故障时会影响出云方向的流量切换。`;
        }
    };
    static filters = {
        noOpenTip(value: string) {
            const isSubUser = window.$context.isSubUser();
            let ResourceType = this.data.get('routeTypeList') || [];
            let serviceText = ResourceType.find((item: any) => item.value === value)?.text;
            let url = serviceTypeUrl[value];
            let hasSubnets = this.data.get('hasSubnets');
            let str = `您的账号当前未开通${serviceText}服务，请前往${serviceText}控制台开通。<a href="${url}">去开通</a>`;
            if (isSubUser) {
                str = `该功能需要开通${serviceText}服务，当前登录的子账号没有开通产品服务的权限，请联系主账号授予开通服务权限，或联系主账户开通服务后授权使用。`;
            }
            if (!hasSubnets && value === 'defaultGateway') {
                str = '请您先创建子网后再添加本地网关路由。';
            }
            return str;
        }
    };
    async attached() {
        this.initIpType();
        this.$http
            .commonQuota({
                serviceType: 'VPC',
                quotaType: 'multRouteRullQuato'
            })
            .then((res: any) => {
                this.data.set('multiRouteQuota', res);
            });
        try {
            await this.initTgwList();
        } catch (e) {}
        let editData = u.cloneDeep(this.data.get('editRule'));
        if (editData) {
            this.initFormData();
            const {dcgwType, ipType, routeType} = this.data.get('formData');
            this.data.set('editRouteType', routeType);
            const nextHopDc = this.data.get('nextHopDc');
            if (dcgwType === 'single') {
                this.data.set('formData.multiType', 'ecmp');
            }
            if (dcgwType === 'multi' && nextHopDc.length !== 1) {
                this.data.set('canChange', false);
            }
            if (dcgwType === 'single' && ipType === '6' && routeType === 'dcGateway') {
                this.data.set('canChange', false);
            }
        }
        await this.getSourceAddress();
        this.getWhiteList();
        this.gwQuota();
        this.watch('nextHopList', list => {
            let isEdit = this.data.get('editRule');
            let value = list?.length ? list[0].value : '';
            !isEdit && this.nextTick(() => this.data.set('formData.nexthopId', value));
            isEdit && isEdit.nexthopType === 'enic' && this.data.set('formData.nexthopId', isEdit.primaryId);
        });
        this.watch('nextHopDc', list => {
            let value = this.filterIpsecVpn(list);
            if (value.findIndex((item: any) => item.nexthopType === 'vpn') > -1) {
                this.data.set('preemptiveDisabled', true);
            } else {
                this.data.set('preemptiveDisabled', false);
            }
        });
    }
    initTgwList() {
        let vpcInfo = this.data.get('vpcInfo');
        if (!vpcInfo.csnId || !window.$storage.get('csnSts')) {
            return;
        }
        let region = window.$context.getCurrentRegionId();
        return this.$http
            .getTgwInstanceList(vpcInfo.csnId, {region, csnAccountId: vpcInfo.csnAccountId})
            .then((res: any) => {
                let tgwInstanceList = res.tgws.map((item: any) => {
                    return {text: item.tgwId, value: item.tgwId};
                });
                this.data.set('tgwInstanceList', tgwInstanceList);
            });
    }

    initFormData(pathType: string) {
        let formData = u.cloneDeep(this.data.get('editRule'));
        pathType && (formData.pathType = pathType);
        if (formData.sourceName === '') {
            formData.customSourceAddress = formData.sourceAddress;
            formData.sourceAddress = 'CUSTOM';
        }
        if (formData.nexthopType) {
            formData.routeType = formData.nexthopType;
        }
        if (formData.nexthopType === RouteType.GATEWAY) {
            this.data.set('nextHopList', [
                {
                    text: '默认网关',
                    value: 'default'
                }
            ]);
            formData.nexthopId = 'default';
            this.data.set('sourceLoad', false);
        }
        if (formData.ipVersion) {
            formData.ipType = formData.ipVersion;
        }
        if (formData.nexthopType === RouteType.GW) {
            formData.dcgwType = formData.pathType === 'normal' ? 'single' : 'multi';
            formData.multiType = formData.pathType;
        }
        this.data.set('nextHopDc', this.initDcList(pathType));
        this.data.set('formData.destinationAddress', formData.destinationAddress);
        this.data.set('formData.sourceAddress', formData.sourceAddress);
        this.data.set('formData', formData);
    }

    initDcList(pathType: string) {
        let formData = this.data.get('editRule');
        let {pathTypes, nexthopIds} = formData;
        let nexthopDc = [];
        nexthopDc = pathTypes.map((item: any, index: number) => {
            return {
                name: '路径' + (index + 1),
                pathType: pathType || pathTypes[index],
                nexthopId: nexthopIds[index],
                nexthopType: RouteType.GW,
                disabled: true
            };
        });
        return nexthopDc;
    }

    async onConfirm() {
        await this.checkFrom();
        await this.checkDcHopList();
        await this.checkOperation();
        this.data.set('confirmed', true);
        let formData = this.getExtraData();
        let title = this.data.get('editRule') ? '编辑成功' : '创建成功';
        let request = this.data.get('editRule')
            ? this.$http.ruleUpdate.bind(this.$http)
            : this.$http.ruleCreate.bind(this.$http);
        request(formData)
            .then((res: any) => {
                this.fire('confirmed');
                Notification.success(title);
                this.closeDialog();
            })
            .catch(err => {
                this.data.set('confirmed', false);
            });
    }

    getExtraData() {
        let formData = this.filterData();
        const nextHopDc = this.data.get('nextHopDc');
        let isMulti = this.data.get('isMulti');
        let showMultiPath = this.data.get('showMultiPath');
        if (!showMultiPath) {
            delete formData.preemptiveMode;
        }
        if (formData.sourceAddress === 'CUSTOM') {
            let mask = formData.customSourceAddress?.split('/')[1];
            let ipType = formData.ipType === IpTypeVersion.IPV4;
            formData.sourceAddress = mask
                ? formData.customSourceAddress
                : formData.customSourceAddress + (ipType ? '/32' : '/128');
            formData.sourceExtra = mask
                ? formData.customSourceAddress
                : formData.customSourceAddress + (ipType ? '/32' : '/128');
        }
        if (isMulti && nextHopDc.length > 1) {
            const nextHopDc = this.data.get('nextHopDc');
            formData.nextHopList = u.map(nextHopDc, item => {
                let {disabled, ...others} = item;
                return {...others};
            });
            formData.nextHopList = this.filterIpsecVpn(formData.nextHopList);
        }
        if (formData.nexthopType === RouteType.GATEWAY) {
            delete formData.nexthopId;
        }
        formData.ipVersion = Number(formData.ipType);
        formData.nexthopType = formData.routeType;
        formData.routeTableId = this.data.get('routeId');
        const vrf = this.data.get('vrf');
        if (vrf === 0 || this.data.get('editRule')) {
            formData.vpcId = this.data.get('vpcId');
        }
        return formData;
    }

    closeDialog() {
        this.dispose();
    }

    checkFrom() {
        if (this.data.get('formData.routeType') === 'custom') {
            return this.data.get('formData.nexthopId') ? this.ref('form').validateFields() : Promise.reject();
        }
        return this.ref('form').validateFields();
    }

    checkDcHopList() {
        // 检查路径重复与没有选择
        let isMulti = this.data.get('isMulti');
        let nextHopDc = this.data.get('nextHopDc');
        let hasError = '';
        let nextHopDcValue = u.pluck(nextHopDc, 'nexthopId');
        this.data.set('nextHopDcErr', '');
        if (!isMulti) {
            return Promise.resolve();
        }
        u.each(nextHopDc, (item, index) => {
            if (hasError !== '') {
                return;
            }
            if (!item.nexthopId) {
                hasError = '请选择下一跳实例';
                return false;
            }
            if (nextHopDcValue.lastIndexOf(item.nexthopId) !== index) {
                hasError = '路径重复！';
                return false;
            }
        });
        this.data.set('nextHopDcErr', hasError);
        return hasError === '' ? Promise.resolve() : Promise.reject();
    }

    checkOperation() {
        let isEdit = this.data.get('editRule');
        let isGw = this.data.get('isGw');
        if (isEdit && isGw) {
            let [changes, changeId] = this.checkDataChange();
            let routeChanges = u.filter(changes, item => item !== 'baseEdit');
            // 无任何操作的情况。
            if (!changes.length) {
                this.closeDialog();
                return Promise.reject();
            } else if (changes.length && !routeChanges.length) {
                // 仅基础数据发生变更，触发update
                return Promise.resolve();
            } else if (routeChanges.length > 1) {
                Notification.warning(
                    '编辑多线路由只允许增加路径、删除路径中的一种，不允许同时进行“增加操作”、“删除操作”。'
                );
                return Promise.reject();
            }
            this.updateMutilRoute(routeChanges[0], changeId);
            return Promise.reject();
        }
        return Promise.resolve();
    }

    deleteMutilRoute(changeId: string) {
        let baseData = this.data.get('editRule');
        let lastHopList = baseData.nexthopIds; // 变化前的路径ID
        let routeRuleIds = changeId.map((item, index) => {
            let findIndex = lastHopList.indexOf(item);
            return baseData.ids[findIndex];
        });
        return this.$http.ruleDelete({routeRuleIds});
    }

    addMutilRoute(changeId: string) {
        let formData = this.data.get('formData');
        formData.nextHopList = [];
        formData.forUpdate = true;
        if (formData.sourceName === '') {
            formData.sourceAddress = formData.customSourceAddress;
            formData.sourceExtra = formData.customSourceAddress;
        }
        changeId.map((item: string) => {
            formData.nextHopList.push({
                nexthopId: item,
                pathType: 'ecmp',
                nexthopType: 'dcGateway'
            });
        });
        return this.$http.ruleCreate(formData);
    }

    updateMutilRoute(type: string, changeId: string) {
        const requestMap = {
            add: this.addMutilRoute.bind(this),
            delete: this.deleteMutilRoute.bind(this)
        };
        const request = requestMap[type];
        this.data.set('confirmed', true);
        request(changeId)
            .then(() => {
                this.fire('confirmed');
                this.closeDialog();
                Notification.success('保存成功');
            })
            .catch(() => {
                Notification.error('保存失败');
                this.data.set('confirmed', false);
            });
    }

    sourceAddressChange(e: any) {
        const {value} = e;
        const routeRules = this.data.get('routeRules');
        const editRule = this.data.get('editRule');
        const formData = this.data.get('formData');
        const existOriginItem = routeRules.find(item => item.sourceAddress === value);
        const mask = value.split('/')[1];
        const existOriginItemMask = existOriginItem?.sourceAddress.split('/')[1];
        if (existOriginItem && editRule?.sourceAddress !== value && existOriginItemMask !== mask) {
            this.data.set(
                'sourceValidateTip',
                `该源网段与已有源网段${existOriginItem.sourceAddress}重叠，请关注路由优先级 <a href=${DocService.route_rule}>路由优先级说明</a>`
            );
        } else if (value.startsWith('0.0.0.0') && formData.destinationAddress?.startsWith('0.0.0.0')) {
            this.data.set('sourceValidateTip', '源网段与目标网段不能同时以0.0.0.0开头');
        } else {
            this.data.set('sourceValidateTip', '');
        }
    }

    checkDataChange() {
        let baseData = this.data.get('editRule');
        let nowData = this.data.get('formData');
        let lastHopList = baseData.nexthopIds; // 变化前的路径ID
        let nowHopList = u.pluck(this.data.get('nextHopDc'), 'nexthopId'); // 变化后的路径ID
        let changes = [];
        let routeChangeId = [];
        // 第一次循环当前项，检测有无新增
        u.each(nowHopList, (item, index) => {
            if (lastHopList.indexOf(item) === -1) {
                routeChangeId.push(item);
                !changes.includes('add') && changes.push('add');
            }
        });

        // 第二次循环变化前的项，检测有无删减
        u.each(lastHopList, (item, index) => {
            if (nowHopList.indexOf(item) === -1) {
                routeChangeId.push(item);
                !changes.includes('delete') && changes.push('delete');
            }
        });

        // 检查基础数据有无改变(描述，目标网段，原网段)
        const nowSourceAddress =
            nowData.sourceAddress === 'CUSTOM' ? nowData.customSourceAddress : nowData.sourceAddress;
        const baseSourceAddress = baseData.sourceAddress;
        const canChange = this.data.get('canChange'); // 专线 可以切换pathType
        if (
            nowData.description !== baseData.description ||
            nowData.destinationAddress !== baseData.destinationAddress ||
            nowSourceAddress !== baseSourceAddress ||
            (canChange && nowData.pathType !== baseData.pathType)
        ) {
            changes.unshift('baseEdit');
        }

        return [changes, routeChangeId];
    }

    ipTypeChange({value}) {
        let routeTypeList;
        let sourceAddressList;
        if (value === IpTypeVersion.IPV4) {
            this.data.set('formData.destinationAddress', '');
            routeTypeList = this.data.get('whiteRouteType');
            sourceAddressList = this.data.get('ipv4List');
        } else {
            this.data.set('formData.destinationAddress', '::/0');
            let vpcInfo = this.data.get('vpcInfo');
            routeTypeList = RouteType.getIpv6Type();
            if (this.data.get('ipv6Show')) {
                routeTypeList = [...routeTypeList, ...RouteType.toArray('GW')];
            }
            if (this.data.get('eniWhite')) {
                routeTypeList = [...routeTypeList, ...RouteType.toArray('ENI')];
            }
            if (vpcInfo.csnId && window.$storage.get('csnSts') && vpcInfo.ipv6Cidr) {
                routeTypeList = [...routeTypeList, ...RouteType.toArray('TGW')];
            }
            const currRegion = window.$context.getCurrentRegionId();
            const isEnableV6 = [AllRegion.SU, AllRegion.NJ, AllRegion.FWH].includes(currRegion);
            if (isEnableV6) {
                routeTypeList = [...routeTypeList, ...RouteType.toArray('NAT')];
            }
            sourceAddressList = this.data.get('ipv6List');
        }
        this.nextTick(() => {
            this.ref('form').validateFields(['destinationAddress']);
        });
        this.data.set('formData.ipType', value);
        this.data.set('sourceAddressList', sourceAddressList);
        if (this.data.get('formData.routeType') !== 'custom') {
            this.data.set('formData.nexthopId', '');
        }
        this.data.set('formData.sourceAddress', '');
        routeTypeList = checkSts(routeTypeList);
        this.data.set('routeTypeList', routeTypeList);
        this.data.set('formData.routeType', routeTypeList[0].value);
        this.nextTick(() => this.data.set('formData.sourceAddress', sourceAddressList[0].value));
        this.loadSource(routeTypeList[0].value);
    }

    dcgwTypeChange({value}) {
        this.data.set('nextHopDcErr', '');
        const editRule = this.data.get('editRule');
        this.data.set('dcgwMultiDisable', false);
        if (editRule && value === 'multi') {
            editRule.pathType === 'normal' && this.data.set('dcgwMultiDisable', true);
            this.data.set('formData.multiType', 'ecmp');
            this.initFormData('ecmp');
        } else if (editRule && value === 'single') {
            this.initFormData('normal');
        }
    }

    checkSourcePattern(value: any) {
        let ipType = this.data.get('formData.ipType');
        return (
            (ipType === IpTypeVersion.IPV4 && RULE.SEG.test(value)) ||
            (ipType === IpTypeVersion.IPV6 && RULE.IPV6_SEG.test(value))
        );
    }

    checkSourceCidr(value: any) {
        let valueString = convertCidrToBinary(value);
        let ipType = this.data.get('formData.ipType');
        let valueMask = value.split('/')[1];
        let vpcInfo = this.data.get('vpcInfo');
        let match = !!this.data.get('relayWhiteList') || !!this.data.get('vpcInfo.relay');
        if (valueString.substring(+valueMask, valueString.length).includes('1')) {
            return 'CIDR格式不合法';
        }
        if (value === '0.0.0.0/0' && !match) {
            return '需打开路由中继才可配置该网段';
        }
        if (!match) {
            if (
                (ipType === IpTypeVersion.IPV4 && vpcInfo.cidr && checkIsInSubnet(value, vpcInfo.cidr)) ||
                (ipType === IpTypeVersion.IPV6 && vpcInfo.ipv6Cidr && checkIsInSubnet(value, vpcInfo.ipv6Cidr))
            ) {
                match = true;
            }
            // 对ipv4辅助网段校验
            if (!match && ipType === IpTypeVersion.IPV4 && vpcInfo.auxiliaryCidr && vpcInfo.auxiliaryCidr.length > 0) {
                for (let i = 0; i < vpcInfo.auxiliaryCidr.length; i++) {
                    if (checkIsInSubnet(value, vpcInfo.auxiliaryCidr[i])) {
                        match = true;
                    }
                }
            }
        }
        if (!match) {
            return '所设置的IP地址，必须属于vpc内的IP地址';
        }
        return '';
    }

    checkTargetCidr(value: any) {
        let vpcCidr = this.data.get('vpcInfo.cidr');
        if (vpcCidr === '0.0.0.0/0') {
            return true;
        }
        let valueString = convertCidrToBinary(value);
        let valueMask = value.split('/')[1] || (value.split('/')[0] === '0.0.0.0' ? 0 : 32);
        if (valueString.substring(+valueMask, valueString.length).includes('1')) {
            return false;
        }
        return true;
    }
    checkRepeatTarget() {
        let formData = this.filterData();
        let map = {};
        let isDestinationAddress = formData.destinationAddress.indexOf('0.0.0.0') === 0;
        // 源网段需区分是否是自定义配置
        const finalSourceAddress =
            formData.sourceAddress === 'CUSTOM' ? formData.customSourceAddress : formData.sourceAddress;
        let isSourceAddress = finalSourceAddress.indexOf('0.0.0.0') === 0;
        if (isSourceAddress && isDestinationAddress) {
            return '源网段与目标网段不能同时以0.0.0.0开头';
        }
        u.each(this.data.get('routeRules'), item => {
            let itemMask = item.destinationAddress ? item.destinationAddress.split('/')[1] || '' : '';
            map[(item.sourceAddress || '-') + convertCidrToBinary(item.destinationAddress) + itemMask] = true;
        });
        // ip地址默认加32
        let mask = formData.destinationAddress ? formData.destinationAddress.split('/')[1] || '' : '';
        if (
            map[formData.sourceAddress + convertCidrToBinary(formData.destinationAddress) + mask] &&
            !this.data.get('editRule')
        ) {
            return '此路由的源网段+目的网段已经存在';
        }
        return '';
    }

    multiTypeChange({value}) {
        let formData = this.data.get('formData');
        let nextHopDc = nextHopDcMap[value];
        this.data.set('nextHopDc', nextHopDc);
        this.data.set('nextHopDcErr', '');
        if (formData.ipType == 6 && value === 'ecmp') {
            this.data.set(
                'nextHopList',
                this.data.get('allGwlist').filter(item => item.enableIpv6)
            );
        } else {
            this.data.set('nextHopList', this.data.get('allGwlist'));
        }
    }

    addPath() {
        let nextHopDc = this.data.get('nextHopDc');
        let index = Number(nextHopDc.length) + 1;
        this.data.push('nextHopDc', {
            name: '路径' + index,
            nexthopId: '',
            nexthopType: RouteType.GW,
            pathType: PathType.ECMP
        });
    }

    filterData() {
        let data = u.cloneDeep(this.data.get('formData'));
        data.destinationAddress = u.trim(data.destinationAddress);
        if (data.destinationAddress === '0.0.0.0') {
            data.destinationAddress = data.destinationAddress + '/0';
        } else if (RULE.IP.test(data.destinationAddress)) {
            data.destinationAddress = data.destinationAddress + '/32';
        } else if (RULE.IPV6.test(data.destinationAddress)) {
            let valueString = convertCidrToBinary(data.destinationAddress);
            if (!valueString.includes('1')) {
                data.destinationAddress = data.destinationAddress + '/0';
            } else {
                data.destinationAddress = data.destinationAddress + '/128';
            }
        }
        return data;
    }

    gwQuota() {
        let routeId = this.data.get('routeId');
        if (this.data.get('editRule')) {
            return;
        }
        this.$http.getGwQuota(routeId).then((res: any) => {
            let {noneMultiType} = checker.check(checkRules, '', 'noneMultiType', {quota: res});
            this.data.set('noneMultiType', noneMultiType);
            this.data.set('ecmpQuota', res.free);
        });
        this.$http.getGwQuota(routeId, 6).then((res: any) => {
            let {ipv6NoneMultiType} = checker.check(checkRules, '', 'ipv6NoneMultiType', {quota: res});
            this.data.set('ipv6NoneMultiType', ipv6NoneMultiType);
            this.data.set('ecmpIpv6Quota', res.free);
        });
    }

    async routeTypeChange({value}) {
        this.nextTick(() => {
            this.ref('form').validateFields(['routeType']);
        });
        if (value === 'defaultGateway') {
            this.data.set('nextHopList', [
                {
                    text: '默认网关',
                    value: 'default'
                }
            ]);
        } else if (value === 'vpc2tgw') {
            this.data.set('nextHopList', this.data.get('tgwInstanceList'));
        } else if (
            (value === 'nat' && !window.$storage.get('natSts')) ||
            (value === 'peerConn' && !window.$storage.get('peerConnSts')) ||
            (value === 'vpn' && !window.$storage.get('vpnSts'))
        ) {
            this.data.set('nextHopList', []);
        } else {
            try {
                if (!this.data.get('ipSecFirst') && value === 'dcGateway') {
                    if (window.$storage.get('vpnSts')) {
                        await this.getIpsecVpnList();
                        this.loadSource(value);
                    } else {
                        this.loadSource(value);
                    }
                } else {
                    this.loadSource(value);
                }
            } catch (error) {
                this.loadSource(value);
            }
        }
    }

    deleteRoutePath(item: any, deleteIndex: number) {
        let nextHopDc = this.data.get('nextHopDc').filter((item: any, index: number) => index !== deleteIndex);
        let nextHopDcList = nextHopDc.map((item: any, index: number) => {
            return {
                name: '路径' + (index + 1),
                nexthopId: item.nexthopId,
                nexthopType: RouteType.GW,
                pathType: PathType.ECMP,
                disabled: item.disabled
            };
        });
        this.data.set('nextHopDc', nextHopDcList);
    }

    loadSource(value: any) {
        let type = value || this.data.get('formData.routeType');
        // 私网NAT支持的region
        const supportPrivateRegion = [
            AllRegion.BJ,
            AllRegion.NJ,
            AllRegion.SU,
            AllRegion.FWH,
            AllRegion.BD,
            AllRegion.GZ
        ];
        const currRegion = window.$context.getCurrentRegionId();
        const isSupportPrivate = u.contains(supportPrivateRegion, currRegion);
        const supportEnhanceRegion = [AllRegion.SU];
        const isSupportEnhanced = u.contains(supportEnhanceRegion, currRegion);
        const requesetMap = {
            nat: this.getNatList,
            vpn: this.getVpnList,
            peerConn: this.getPeerConnList,
            dcGateway: this.getGwList,
            ipv6gateway: this.getIpv6Detail,
            enic: this.getEniList,
            havip: this.getHaVipList
        };
        if (!requesetMap[type]) {
            return;
        }
        this.data.set('sourceLoad', true);
        return requesetMap[type].apply(this).then((res: any) => {
            this.data.set('nextHopList', res);
            this.data.set('sourceLoad', false);
            return Promise.resolve();
        });
    }

    getSourceAddress() {
        this.data.set('loadSource', true);
        return this.$http
            .vpcSubnetList({vpcId: this.data.get('vpcId'), attachVm: false})
            .then((data: any) => {
                this.data.set('loadSource', false);
                this.data.set('hasSubnets', data && data.length > 0);
                let ipv4List = [];
                let ipv6List = [];
                u.each(data, item => {
                    if (item.ipv6Cidr) {
                        ipv6List.push({
                            value: item.ipv6Cidr,
                            text: item.name + (item.ipv6Cidr ? '（' + item.ipv6Cidr + '）' : ''),
                            cidr: item.ipv6Cidr,
                            subnetType: item.subnetType
                        });
                    }
                    ipv4List.push({
                        value: item.cidr,
                        text: item.name + (item.cidr ? '（' + item.cidr + '）' : ''),
                        cidr: item.cidr,
                        subnetType: item.subnetType
                    });
                });
                ipv4List.push({
                    text: '自定义配置',
                    value: 'CUSTOM'
                });
                ipv6List.push({
                    text: '自定义配置',
                    value: 'CUSTOM'
                });
                let editRule = this.data.get('editRule');
                this.data.set('subnetList', data);
                this.data.set('ipv4List', ipv4List);
                this.data.set('ipv6List', ipv6List);
                this.loadSource();
                if (!editRule) {
                    this.data.set('sourceAddressList', this.data.get('quota') ? ipv4List : ipv6List);
                    this.data.set(
                        'formData.sourceAddress',
                        this.data.get('quota') ? ipv4List[0].value : ipv6List[0].value
                    );
                } else {
                    let ipType = editRule.ipVersion;
                    this.data.set('sourceAddressList', ipType === '4' ? ipv4List : ipv6List);
                }
            })
            .catch(err => {
                this.data.set('loadSource', false);
            });
    }

    getVpnList() {
        let query = {vpcId: this.data.get('vpcId')};
        return this.$http.getVpnList(query, kXhrOptions).then((data: any) => {
            let result = [];
            u.each(data.result, item => {
                if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                    result.push({
                        value: item.vpnId,
                        text: `${item.vpnName}/${item.vpnId}`
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    // 公网NAT，包含v4和v6
    getNatList(name: string) {
        const cidrType = this.data.get('formData.ipType');
        let query = {
            vpcId: this.data.get('vpcId')
            // pageNo: 1,
            // pageSize: 10000
        };
        if (name) {
            query.name = name;
        }
        return this.$http.getNatListForRoute(query, kXhrOptions).then((res: any) => {
            let result = [];
            // let eips = '';
            // let text = '';
            let title = '';
            if (res) {
                const dataList =
                    cidrType === '4'
                        ? _.filter(res.list, item => item.ipVersion === 'v4' || !item.ipVersion)
                        : _.filter(res.list, item => item.ipVersion === 'v6');
                u.each(dataList, item => {
                    // 只显示active状态的nat
                    // if (u.indexOf([NatStatus.ACTIVE], item.status) > -1) {
                    // eips = u.pluck(item.eips, 'eip').join('、');
                    // text = item.name + (eips ? '（'
                    //     + (eips.length > 14 ? (eips.substring(0, 14) + '...') : eips) + '）' : '');
                    // title = item.name + (eips ? '（' + eips + '）' : '');
                    title = `${item.name}(${item.id})`;
                    result.push({
                        value: item.id,
                        text: title
                    });
                    // }
                });
            }
            return Promise.resolve(result);
        });
    }

    // 特定region
    getPrivateNatList(name: string) {
        const cidrType = this.data.get('formData.ipType');
        let query = {
            vpcId: this.data.get('vpcId')
        };
        if (name) {
            query.name = name;
        }
        return this.$http.getNatListForRoute(query, kXhrOptions).then((res: any) => {
            let result = [];
            // let eips = '';
            // let text = '';
            let title = '';
            if (res) {
                const dataList =
                    cidrType === '4'
                        ? _.filter(res.list, item => item.ipVersion === 'v4')
                        : _.filter(res.list, item => item.ipVersion === 'v6');
                u.each(dataList, item => {
                    // 只显示active状态的nat
                    // eips = u.pluck(item.eips, 'eip').join('、');
                    // text = item.name + (eips ? '（'
                    //     + (eips.length > 14 ? (eips.substring(0, 14) + '...') : eips) + '）' : '');
                    // title = item.name + (eips ? '（' + eips + '）' : '');
                    title = `${item.name}(${item.id})`;
                    result.push({
                        value: item.id,
                        text: title
                    });
                });
            }
            return Promise.resolve(result);
        });
    }

    getPeerConnList(localIfName: string) {
        let query = {
            localVpcShortId: this.data.get('vpcInfo').shortId,
            pageNo: 1,
            pageSize: 10000
        };
        if (localIfName) {
            query.localIfName = localIfName;
        }
        return this.$http.peerconnList(query, kXhrOptions).then((res: any) => {
            let result = [];
            u.each(res.result, item => {
                if (u.indexOf([PeerConnStatus.ACTIVE], item.status) > -1) {
                    let name = item.localIfName + '（' + item.localIfId + '）';
                    result.push({
                        value: item.localIfId,
                        text: name
                    });
                }
            });
            return Promise.resolve(result);
        });
    }

    getGwList(name: string) {
        let query = {
            vpcId: this.data.get('vpcInfo').shortId,
            pageNo: 1,
            pageSize: 10000
        };
        if (name) {
            query.keywordType = 'name';
            query.keyword = name;
        }
        return this.$http.dcgwList(query, kXhrOptions).then((res: any) => {
            let result = [];
            let formData = this.data.get('formData');
            u.each(res.result, item => {
                if (u.indexOf([DcGatewayStatus.RUNNING], item.status) > -1) {
                    let name = item.name + '/' + item.id;
                    result.push({
                        value: item.id,
                        text: name,
                        enableIpv6: item.enableIpv6
                    });
                    this.data.set('allGwlist', result);
                    if (formData.ipType == 6 && formData.multiType === 'ecmp') {
                        result = result.filter(item => item.enableIpv6);
                    }
                }
            });
            return Promise.resolve(result);
        });
    }

    getHaVipList() {
        let query = {
            vpcId: this.data.get('vpcId'),
            pageNo: 1,
            pageSize: 10000
        };
        return this.$http.getHaVipList(query, kXhrOptions).then((data: any) => {
            let result = [];
            u.each(data.result, item => {
                result.push({
                    value: item.haVipId,
                    text: `${item.name}（${item.internalIp}）`
                });
            });
            return Promise.resolve(result);
        });
    }

    getIpv6Detail() {
        return this.$http.ipv6Detail({vpcId: this.data.get('vpcInfo').vpcId}, kXhrOptions).then((data: any) => {
            let result = [];
            if (data && data.gatewayId) {
                result.push({
                    value: data.gatewayId,
                    text: data.name
                });
            }
            return Promise.resolve(result);
        });
    }

    getSubnetIdsByValue() {
        return u.pluck(this.data.get('subnetList'), 'subnetId');
    }

    getWhiteList() {
        let all = [
            this.enableVpnRegion(),
            this.natWhiteList(),
            this.peerconnWhiteList(),
            this.relayWhiteList(),
            this.getIpv6White(),
            this.routeEniWhiteList()
        ];
        Promise.all(all).then(white => {
            let routeTypeList: any;
            let region = window.$context.getCurrentRegionId();
            let eniWhite = white[5].regions?.indexOf(region) > -1;
            let [VPN, NAT, PEERCONN, relayWhiteList] = white;
            let whiteList = ['CUSTOM', 'TGW'];
            if (eniWhite) {
                whiteList.push('ENI');
                this.data.set('eniWhite', true);
            }
            let editRule = this.data.get('editRule');
            const whiteMap = {
                NAT: NAT,
                GATEWAY: true,
                VPN: VPN,
                PEERCONN: PEERCONN,
                GW: true
            };
            u.each(whiteMap, (item, key) => {
                item && whiteList.push(key);
            });
            if (window.$storage.get('commonWhite')?.HaVipWhiteList) {
                whiteList.push('HAVIP');
            }
            if (editRule) {
                let ipType = editRule.ipVersion;
                let vpcInfo = this.data.get('vpcInfo');
                routeTypeList = ipType === '4' ? RouteType.getIpv4Type() : RouteType.getIpv6Type();
                if (this.data.get('ipv6Show') && ipType === '6') {
                    routeTypeList = [...routeTypeList, ...RouteType.toArray('GW')];
                }
                if (this.data.get('eniWhite')) {
                    routeTypeList = [...routeTypeList, ...RouteType.toArray('ENI')];
                }
                if (ipType === '6' && vpcInfo.csnId && window.$storage.get('csnSts') && vpcInfo.ipv6Cidr) {
                    routeTypeList = [...routeTypeList, ...RouteType.toArray('TGW')];
                }
            } else {
                routeTypeList = RouteType.toArray(...whiteList);
            }
            routeTypeList = checkSts(routeTypeList);
            let routeTypeArr = routeTypeList.map(item => {
                if (!this.data.get('hasSubnets') && item.value === 'defaultGateway') {
                    return {
                        ...item,
                        disabled: true
                    };
                }
                return item;
            });
            this.data.set('whiteRouteType', routeTypeArr);
            this.data.set('routeTypeList', routeTypeArr);
            this.data.set('relayWhiteList', relayWhiteList);
        });
    }
    inputChange({value}) {
        let type = this.data.get('formData.ipType');
        if (type === '4') {
            if (value === '0.0.0.0' || value === '0.0.0.0/0') {
                this.data.set('showTip', true);
            } else {
                this.data.set('showTip', false);
            }
        }
    }
    natWhiteList() {
        return true;
    }

    peerconnWhiteList() {
        const serversType = Object.keys(window.$context.SERVICE_TYPE);
        return serversType.includes('PEERCONN') && window.$context.getCurrentRegionId() !== AllRegion.BJKS;
    }

    relayWhiteList() {
        const whiteList = window.$storage.get('commonWhite');
        return Promise.resolve(whiteList?.RelayWhiteList);
    }

    enableVpnRegion() {
        let region = window.$context.getCurrentRegionId();
        if (region === AllRegion.HK02) {
            return true;
        }
        return u.indexOf(disable_vpn_region, region) === -1;
    }
    getIpv6White() {
        const whiteList = window.$storage.get('commonWhite');
        this.data.set('ipv6Show', whiteList?.DedicatedConnIpv6);
        return Promise.resolve(whiteList?.DedicatedConnIpv6);
    }
    getIpsecVpnList() {
        let query = {
            vpcId: this.data.get('vpcId'),
            pageNo: 1,
            pageSize: 10000,
            vpnType: 'ipsec'
        };
        this.data.set('ipSecFirst', true);
        return this.$http.getVpnList(query, kXhrOptions).then((data: any) => {
            let result = [];
            u.each(data.result, item => {
                if (u.indexOf([VpnStatus.ACTIVE], item.status) > -1) {
                    result.push({
                        value: item.vpnId,
                        text: `${item.vpnName}/${item.vpnId}`
                    });
                }
            });
            this.data.set('vpnIpsecList', result);
        });
    }
    filterIpsecVpn(nextHopList: any) {
        let vpnIpsecList = this.data.get('vpnIpsecList');
        let index = nextHopList.findIndex((item: any) => item.name === '备路径');
        if (index > -1) {
            let array = u.cloneDeep(nextHopList[index]);
            if (vpnIpsecList.findIndex((item: any) => item.value === array.nexthopId) > -1) {
                array.nexthopType = 'vpn';
            } else {
                array.nexthopType = 'dcGateway';
            }
            nextHopList.splice(index, 1, array);
        }
        return nextHopList;
    }
    getEniList() {
        let param = {
            vpcId: this.data.get('vpcId'),
            source: 'default',
            pageNo: 1,
            pageSize: 10000
        };
        let ipType = this.data.get('formData.ipType');
        return this.$http.getEniList(param, kXhrOptions).then((res: any) => {
            let result = [];
            u.each(res.result, item => {
                if (ipType === '6') {
                    if (
                        item.ipv6Ips.length > 0 &&
                        u.indexOf([EniStatus.AVAILABLE, EniStatus.INUSE], item.status) > -1
                    ) {
                        result.push({
                            value: item.eniUuid,
                            text: `${item.name}(${item.eniId})`
                        });
                    }
                } else {
                    if (u.indexOf([EniStatus.AVAILABLE, EniStatus.INUSE], item.status) > -1) {
                        result.push({
                            value: item.eniUuid,
                            text: `${item.name}(${item.eniId})`
                        });
                    }
                }
            });
            return Promise.resolve(result);
        });
    }
    routeEniWhiteList() {
        return this.$http.routeEniWhiteList();
    }
    checkSourceIP(value: any) {
        let ipType = this.data.get('formData.ipType');
        return (
            (ipType === IpTypeVersion.IPV4 && RULE.IP.test(value)) ||
            (ipType === IpTypeVersion.IPV6 && RULE.IPV6.test(value))
        );
    }
    // 根据配额设置网络类型
    initIpType() {
        let quota = this.data.get('quota');
        let v6Quota = this.data.get('v6Quota');
        this.data.set('formData.ipType', quota <= 0 ? '6' : '4');
        let {createV4Route, createV6Route} = checker.check(checkRules, '', '', {quota, v6Quota});
        this.data.set('v4Disable', createV4Route);
        this.data.set('v6Disable', createV6Route);
    }
    customSelect(value: any) {
        this.data.set('formData.nexthopId', value?.instanceUuid);
    }
}
export default Processor.autowireUnCheckCmpt(RouteCreate);
