/**
 * <AUTHOR>
 */
import {Component} from 'san';
import u from 'lodash';
import {AppOrderPage} from '@baidu/sui-biz';
import {Button, Dialog} from '@baidu/sui';
import {decorators, html, redirect, Processor} from '@baiducloud/runtime';
import {San2React} from '@baidu/bce-react-toolkit';
import {StsConfig} from '@/pages/sanPages/utils/config';
import {isOnline} from '@/pages/sanPages/utils/common';
import {getUserId} from '@/pages/sanPages/utils/helper';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import {DocService} from '@/pages/sanPages/common/docs';
import {EventBus, EventName, activeServiceType} from '@/utils';
import './style.less';

const {template} = decorators;
const AllRegion = window.$context.getEnum('AllRegion');

/* eslint-disable */
const tpl = html`
    <div>
        <div class="auth nat-auth">
            <div class="auth-title">NAT 网关</div>
            <s-order-page
                title="{{title}}"
                desc="{{desc}}"
                logoSrc="{{logoSrc}}"
                process="{{process}}"
                useNewVersion="{{true}}"
                agreed="{{true}}"
                disabled="{{disabled}}"
                advantage="{{advantage}}"
                scene="{{scene}}"
                feature="{{feature}}"
                on-click="open"
                openBtnDisabled="{{openBtnDisabled}}"
                openBtnDisabledTip="{{openBtnDisabledTip}}"
            >
                <div slot="desc">
                    <span s-html="natIntroText"></span>
                </div>
                <div slot="scene-content" class="scene-content">
                    <div s-for="item, indexs in scene.content">
                        <div s-if="{{index === indexs}}">
                            <div class="img-title-wrapper">
                                <img class="item-img" src="{{item.imgSrc}}" />
                                <div class="item-title">{{item.title}}</div>
                            </div>
                            <div class="content-item" s-for="content, idx in item.contentArr">
                                <div class="subTitle-class">{{content.title}}</div>
                                <div class="subDesc-class">{{content.desc}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div slot="feature-content" class="scene-content">
                    <div s-for="item, indexs in feature.content">
                        <div s-if="{{index === indexs}}">
                            <div class="img-title-wrapper">
                                <img class="item-img" src="{{item.imgSrc}}" />
                                <div class="item-title">{{item.title}}</div>
                            </div>
                            <div class="subDesc-class">{{item.desc}}</div>
                            <s-button skin="stringfy" on-click="openImgDialog(item.link)" class="scene-btn"
                                >查看示意图</s-button
                            >
                        </div>
                    </div>
                </div>
            </s-order-page>
            <s-dialog
                open="{=preImgDialog=}"
                width="{{800}}"
                height="{{560}}"
                title="{{'图片预览'}}"
                class="nat-auth-dialog"
            >
                <div class="img-preview">
                    <img src="{{preImgSrc}}" />
                </div>
                <div slot="img-preview-footer"></div>
                <div slot="footer"></div>
            </s-dialog>
            <div></div>
        </div>
    </div>
`;
/* eslint-enable */

@template(tpl)
class NatAuth extends Component {
    static components = {
        's-order-page': AppOrderPage,
        's-button': Button,
        's-dialog': Dialog
    };

    static computed = {
        natIntroText() {
            const docUrl = this.data.get('DocService.nat_index');
            const isXS = this.data.get('FLAG.NetworkSupportXS');

            if (!isXS) {
                return `NAT（NETWORK Address Translation）网关为私有网络提供访问Internet服务，支持SNAT和DNAT可以使多台云服务器共享公网IP资源访问Internet，也可以使云服务器能够提供Internet服务，NAT网关可以绑定EIP实例及共享带宽，为云服务器实现从内网IP到公网IP的多对一或多对多的地址转换服务。查看<a href="${docUrl}" target="_blank">帮助文档</a>`;
            } else {
                return 'NAT（NETWORK Address Translation）网关为私有网络提供访问Internet服务，支持SNAT和DNAT可以使多台云服务器共享公网IP资源访问Internet，也可以使云服务器能够提供Internet服务，NAT网关可以绑定EIP实例及共享带宽，为云服务器实现从内网IP到公网IP的多对一或多对多的地址转换服务。查看帮助文档';
            }
        }
    };

    initData() {
        return {
            DocService,
            FLAG,
            title: 'NAT 网关',
            logoSrc: 'https://bce.bdstatic.com/network-frontend/nat-intro-2.png',
            advantage: {
                title: '产品优势',
                content: [
                    {
                        title: '高性能',
                        desc: '支持高达百万级的吞吐性能，百万级连接数，满足超大业务上云需求。',
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/nat-intro-5.png'
                    },
                    {
                        title: '高可用',
                        desc: 'NAT网关采用跨机房容灾部署，实现网关服务高可用。',
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/nat-intro-4.png'
                    },
                    {
                        title: '低成本',
                        desc: '多个弹性云服务器共享使用弹性公网IP，有效降低成本。',
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/nat-intro-1.png'
                    },
                    {
                        title: '易管理',
                        desc: '进行简单NAT网关配置即可使用，运维简单，即开即用。',
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/nat-intro-6.png'
                    }
                ]
            },
            scene: {
                title: '核心能力',
                content: [
                    {
                        title: 'SNAT功能',
                        contentArr: [
                            {
                                title: '源网络地址转换',
                                desc: '为VPC内无公网IP的云服务器提供访问互联网的代理服务。'
                            },
                            {
                                title: '简易防火墙',
                                desc: '可以作为一个简易防火墙使用，保护私有网络信息不直接暴露公网。'
                            }
                        ],
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/nat-snat.png'
                    },
                    {
                        title: 'DNAT功能',
                        contentArr: [
                            {
                                title: '目的网络地址转换',
                                desc: '目的网络地址转换，将NAT网关上的公网IP映射给云服务器实例使用。'
                            },
                            {
                                title: '多种映射支持',
                                desc: '支持IP映射和端口映射，使云服务器实例能够提供互联网服务。'
                            }
                        ],
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/nat-dnat.png'
                    }
                ]
            },
            feature: {
                title: '应用场景',
                content: [
                    {
                        title: '主动访问公网',
                        desc: '基于私有网络VPC搭建多个应用，各应用都需要对外提供服务。',
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/nat-intro-7.png',
                        link: 'https://bce.bdstatic.com/p3m/common-service/uploads/NAT1_2945d84.png'
                    },
                    {
                        title: '对外提供服务',
                        desc: '提供DNAT端口级转发功能，使云上业务可轻松面向Internet提供服务，并同时节省大量弹性公网IP。',
                        imgSrc: 'https://bce.bdstatic.com/network-frontend/nat-intro-3.png',
                        link: 'https://bce.bdstatic.com/p3m/common-service/uploads/NAT2_53abc88.png'
                    }
                ]
            },
            disabled: false,
            preImgDialog: false,
            openBtnDisabled: false,
            openBtnDisabledTip: ''
        };
    }
    inited() {
        const isSubUser = window.$context.isSubUser();
        if (isSubUser) {
            this.data.set('openBtnDisabled', true);
            this.data.set('openBtnDisabledTip', '当前登录的子账户没有开通服务的权限，请联系主账户开通服务后使用。');
        }
    }
    open() {
        const roleName = StsConfig.NAT.roleName;
        return this.$http
            .iamStsRoleActivate(
                u.extend(
                    {
                        roleName,
                        accountId: getUserId()
                    },
                    isOnline() ? StsConfig.NAT.online : StsConfig.NAT.sandbox
                ),
                {region: AllRegion.BJ}
            )
            .then(() => {
                EventBus.fire(EventName.productActive, activeServiceType.nat);
                window.$storage.set('natSts', true);
            });
    }
    openImgDialog(link) {
        this.data.set('preImgDialog', true);
        this.data.set('preImgSrc', link);
    }
}
export default San2React(Processor.autowireUnCheckCmpt(NatAuth));
