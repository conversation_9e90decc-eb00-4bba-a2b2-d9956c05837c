/**
 * @file acl操作禁用配置
 * <AUTHOR>
 */

import {ContextService} from '../../common';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';

const currentModule = 'ACL';
export default {
    addRuleIn: [
        {
            required: false,
        },
        {
            custom(data, options) {
                if (!options.ruleOutQuotaCheck) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '子网ACL规则配额不足'
                        };
                    }
                    else {
                        return {
                            disable: true,
                            message: `子网ACL规则配额不足，<a href="/quota_center/#/quota/apply/create?serviceType=${currentModule}&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=aclRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    addRuleOut: [
        {
            required: false,
        },
        {
            custom(data, options) {
                if (!options.ruleOutQuotaCheck) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '子网ACL规则配额不足'
                        };
                    }
                    else {
                        return {
                            disable: true,
                            message: `子网ACL规则配额不足，<a href="/quota_center/#/quota/apply/create?serviceType=${currentModule}&region=${window?.$context?.getCurrentRegionId()}&cloudCenterQuotaName=aclRuleQuota" target="_blank">去申请配额</a>`
                        };
                    }
                }
            }
        }
    ],
    addMoreRule: [
        {
            required: false,
        },
        {
            custom(data, options = {}) {
                if (options.quota === 10 && options.free <= 0) {
                    return {
                        disable: true,
                        message: '一次最多可添加10个规则，可多次添加'
                    };
                }// 配额足够的情况下
                else if (options.free <= 0) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: '子网ACL规则配额不足'
                        };
                    }
                    else {
                        return {
                            disable: true,
                            message: `子网ACL规则配额不足。如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    deleteRulesIn: [
        {
            required: true,
            message: '请先选择实例对象'
        },
    ],
    deleteRulesOut: [
        {
            required: true,
            message: '请先选择实例对象'
        },
    ],
    addVPCAclRuleIn: [
        {
            required: false,
        },
        {
            custom(data, options) {
                if (!options.ruleInVPCQuotaCheck) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'VPC内所有ACL规则配额不足'
                        };
                    }
                    else {
                        return {
                            disable: true,
                            message: `VPC内所有ACL规则配额不足。如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    addVPCAclRuleOut: [
        {
            required: false,
        },
        {
            custom(data, options) {
                if (!options.ruleOutVPCQuotaCheck) {
                    if (FLAG.NetworkSupportXS) {
                        return {
                            disable: true,
                            message: 'VPC内所有ACL规则配额不足'
                        };
                    }
                    else {
                        return {
                            disable: true,
                            message: `VPC内所有ACL规则配额不足。如需增加配额请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                        };
                    }
                }
            }
        }
    ],
    ipv6Ruletype: [
        {
            required: false,
        },
        {
            custom(data, options = {}) {
                if (!options.ipv6Cidr) {
                    return {
                        disable: true,
                        message: '该子网无IPv6网段，请先添加IPv6网段'
                    };
                }
            }
        }
    ]
};
