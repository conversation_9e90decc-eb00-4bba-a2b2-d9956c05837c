/*
 * @Description:
 * @Author: <EMAIL>
 * @Date: 2022-04-06 13:50:58
 */
import {ContextService} from '@/pages/sanPages/common';
import RULE from '@/pages/sanPages/utils/rule';
import {checkIsInSubnet, convertCidrToBinary} from '@/pages/sanPages/utils/common';
import {$flag as FLAG, getUserId, validateAccountIsSame} from '@/pages/sanPages/utils/helper';

const mskTrans = digit => {
    let add = 128 - Number(digit);
    let str1 = '';
    let str2 = '';
    for (let i = 0; i < Number(digit); i++) {
        str1 += '1';
    }
    for (let i = 0; i < add; i++) {
        str2 += '0';
    }
    let msk_str_b = str1 + str2;
    return msk_str_b;
};

const ipv6FirstAdd = (partIp, partMask) => {
    let part_ip = parseInt(partIp, 2);
    let part_mask = parseInt(partMask, 2);
    let ip_min = part_ip & part_mask;
    let s_ip_min = ip_min.toString(16).startsWith('0x') ? ip_min.toString(16).slice(2) : ip_min.toString(16);
    return s_ip_min;
};

export const validateRules = self => {
    return {
        dcphyId: [{required: true, message: '请选择物理专线'}],
        name: [
            {required: true, message: '名称必填'},
            {
                validator(rule, value, callback) {
                    let pattern = /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5\w\-\_\/\.]{0,64}$/;
                    if (!pattern.test(value)) {
                        return callback(
                            '长度限制为1-65个字符，支持大小写字母、数字、中文以及-_/.特殊字符，必须以字母或者中文开头'
                        );
                    }
                    if (value === 'default') {
                        return callback('专线名称不能是default');
                    }
                    return callback();
                }
            }
        ],
        authorizedUsers: [
            {required: true, message: '账户ID必填'},
            {
                validator(rule, value, callback) {
                    if (value === getUserId() || validateAccountIsSame(value)) {
                        return callback('跨账号分配时账户ID不能为本账号');
                    }
                    return callback();
                }
            }
        ],
        vlanId: [
            {required: true, message: 'VLAN ID必填'},
            {
                validator(rule, value, callback) {
                    if (value !== 0 && (value < 2 || value > 4009)) {
                        return callback('VLAN ID范围0, 2-4009');
                    }
                    if (self.data.get('hasNotVLAN0') && value === 0) {
                        return callback('该模式下不允许创建vlan0的专线通道，如需创建请删除非vlan0的专线通道');
                    }
                    return callback();
                }
            }
        ],
        bgpKey: [
            {
                pattern: /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[!@#$%*()_.])[\da-zA-Z!@#$%*()_.]{8,80}$/,
                message: '8～80位字符，英文、数字和符号必须同时存在，符号仅限!@#$%*()_.'
            }
        ],
        bgpAsn: [
            {required: true, message: 'ASN 必填'},
            {
                validator(rule, value, callback) {
                    let pattern = /^[1-9]\d*$/;
                    if (+value === 45085) {
                        const asnErrorMsg = FLAG.NetworkSupportXS 
                            ? '45085为智能云ASN，请输入其他ASN'
                            : `45085为${ContextService.ProjectName}ASN，请输入其他ASN`;
                        return callback(asnErrorMsg);
                    }
                    if (!pattern.test(value) || +value > 4294967295) {
                        return callback('格式错误');
                    }
                    return callback();
                }
            }
        ],
        description: [
            {
                validator(rule, value, callback) {
                    if (value !== undefined && value.length > 200) {
                        return callback('最大输入200个字符');
                    }
                    return callback();
                }
            }
        ],
        localIpv6: [
            {
                validator(rule, value, callback) {
                    if (self.data.get('formData.enableIpv6')) {
                        if (!value) {
                            return callback('请输入');
                        }
                        if (!RULE.IPV6_SEG.test(value)) {
                            return callback('CIDR格式不合法');
                        }
                        let localMask = Number(value.split('/')?.[1]);
                        let remoteIpv6Mask = Number(self.data.get('formData.remoteIpv6').split('/')?.[1]);
                        // 要求输入cidr为2400:DA00:E003:0000:016A:0400:0000:100/（88-127）只比较前88位
                        // 用于比较的二进制
                        const cidrBinary = convertCidrToBinary('2400:DA00:E003:0000:016A:0400:0000:100/128') + '';
                        const cidrBinary88 = cidrBinary.slice(0, 88);
                        let valueBinary = convertCidrToBinary(value) + '';
                        let valueBinarySub = valueBinary.slice(0, localMask);
                        if (!valueBinarySub.startsWith(cidrBinary88)) {
                            return callback('请按示例网段进行输入');
                        }
                        if (localMask < 88 || localMask > 127) {
                            return callback('掩码范围为88-127');
                        }
                        let remoteCidr = self.data.get('formData.remoteIpv6');
                        if (!checkIsInSubnet(value + (/\//g.test(value) ? '' : '/128'), remoteCidr)) {
                            return callback('IPv6互联地址必须在同一网段');
                        }
                        if (self.data.get('formData.remoteIpv6') === value) {
                            return callback('云端网络侧IPv6互联地址不能和IDC侧IPv6互联地址重复');
                        }
                        if (remoteIpv6Mask && localMask !== remoteIpv6Mask) {
                            return callback('IPv6网段的掩码一致，才能进行创建专线通道');
                        }
                        // 掩码长度小于 127 的 IPv6 地址，不能使用网段的第一个地址
                        let firstAdd = ipv6FirstAdd(valueBinary.slice(112, 128), mskTrans(localMask).slice(112, 128));
                        if (localMask < 127 && Number(value.split('/')[0].split(':')[7]) === Number(firstAdd)) {
                            return callback('不能使用网段的第一个地址');
                        }
                        return callback();
                    }
                    return callback();
                }
            }
        ],
        remoteIpv6: [
            {
                validator(rule, value, callback) {
                    if (self.data.get('formData.enableIpv6')) {
                        if (!value) {
                            return callback('请输入');
                        }
                        if (!RULE.IPV6_SEG.test(value)) {
                            return callback('CIDR格式不合法');
                        }
                        let remoteMask = Number(value.split('/')?.[1]);
                        let localIpv6Mask = Number(self.data.get('formData.localIpv6').split('/')?.[1]);
                        // 要求输入cidr为2400:DA00:E003:0000:016A:0400:0000:100/（88-127）只比较前88位
                        // 用于比较的二进制
                        const cidrBinary = convertCidrToBinary('2400:DA00:E003:0000:016A:0400:0000:100/128') + '';
                        const cidrBinary88 = cidrBinary.slice(0, 88);
                        let valueBinary = convertCidrToBinary(value) + '';
                        let valueBinarySub = valueBinary.slice(0, remoteMask);
                        if (!valueBinarySub.startsWith(cidrBinary88)) {
                            return callback('请按示例网段进行输入');
                        }
                        if (remoteMask < 88 || remoteMask > 127) {
                            return callback('掩码范围为88-127');
                        }
                        let localCidr = self.data.get('formData.remoteIpv6');
                        if (!checkIsInSubnet(value + (/\//g.test(value) ? '' : '/128'), localCidr)) {
                            return callback('IPv6互联地址必须在同一网段');
                        }
                        if (self.data.get('formData.localIpv6') === value) {
                            return callback('IDC侧IPv6互联地址不能和云端网络侧IPv6互联地址重复');
                        }
                        if (localIpv6Mask && remoteMask !== localIpv6Mask) {
                            return callback('IPv6网段的掩码一致，才能进行创建专线通道');
                        }
                        // 掩码长度小于 127 的 IPv6 地址，不能使用网段的第一个地址
                        let firstAdd = ipv6FirstAdd(valueBinary.slice(112, 128), mskTrans(remoteMask).slice(112, 128));
                        if (remoteMask < 127 && Number(value.split('/')[0].split(':')[7]) === Number(firstAdd)) {
                            return callback('不能使用网段的第一个地址');
                        }
                        return callback();
                    }
                    return callback();
                }
            }
        ],
        fakeAsn: [
            {required: true, message: 'Fake ASN必填'},
            {
                validator(rule, value, callback) {
                    let pattern = /^[1-9]\d*$/;
                    if (+value === 45085) {
                        const asnErrorMsg = FLAG.NetworkSupportXS 
                            ? '45085为智能云ASN，请输入其他ASN'
                            : `45085为${ContextService.ProjectName}ASN，请输入其他ASN`;
                        return callback(asnErrorMsg);
                    }
                    if (!pattern.test(value) || +value > 4294967295) {
                        return callback('格式错误');
                    }
                    if (value === bgpAsn) {
                        return callback('Fake ASN不能与BGP ASN相同');
                    }
                    return callback();
                }
            }
        ]
    };
};
