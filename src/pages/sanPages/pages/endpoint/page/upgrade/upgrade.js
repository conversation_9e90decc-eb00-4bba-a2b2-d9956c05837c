import u from 'lodash';
import {Component} from 'san';
import {html, decorators, redirect, Processor} from '@baiducloud/runtime';
import {Notification} from '@baidu/sui';
import {San2React, getQueryParams} from '@baidu/bce-react-toolkit';

import {ContextService, PayType, EndpointStatus} from '@/pages/sanPages/common';
import {kXhrOptions} from '@/pages/sanPages/utils/helper';
import {$flag as FLAG} from '@/pages/sanPages/utils/helper';
import './style.less';

const {invokeSUIBIZ, invokeSUI, template, service} = decorators;
/* eslint-disable */
const tpl = html`<template>
    <s-app-create-page
        class="vpc-enic-upgrade"
        pageTitle="{{pageNav.title}}"
        backToLabel="{{pageNav.backLabel}}"
        backTo="{{pageNav.backUrl}}"
    >
        <s-form formData="{=formData=}" label-align="left">
            <div class="body-part-content form-part-wrap" style="background:#F5F5F5">
                <h4>当前配置</h4>
                <ul class="content-item-box">
                    <li class="content-item">
                        <label class="cell-title">运行状态：</label>
                        <span class="{{instance.status | statusStyle}}">{{instance.status | statusText}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">内网IP：</label>
                        <span class="cell-content">{{instance.ovip}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">计费名称：</label>
                        <span class="cell-content">后付费</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">服务域名：</label>
                        <span class="cell-content">{{instance.service}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">所在网络：</label>
                        <span class="cell-content"
                            ><a href="#/vpc/instance/list"
                                >{{vpcInfo.name}}<span s-f="vpcInfo.cidr">（{{vpcInfo.cidr}}）</span></a
                            ></span
                        >
                    </li>
                    <li class="content-item">
                        <label class="cell-title">所在子网：</label>
                        <span class="cell-content">
                            <a href="#/vpc/subnet/list?vpcId={{vpcInfo.vpcId}}">
                                {{instance.subnetName}}
                                <span s-f="instance.subnetCidr"> （{{instance.subnetCidr}}） </span>
                            </a>
                        </span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">地域：</label>
                        <span class="cell-content">{{instance.showRegion}}</span>
                    </li>
                    <li class="content-item">
                        <label class="cell-title">带宽：</label>
                        <span class="cell-content">{{instance.bandwidth || '-'}}Mbps</span>
                    </li>
                </ul>
            </div>
            <div class="body-part-content form-part-wrap">
                <h4>变更配置</h4>
                <s-form-item label="{{'带宽峰值：'}}" prop="bandwidth" class="form-slider-wrap">
                    <s-slider
                        ref="drag"
                        parts="{{2}}"
                        width="450"
                        marks="{{bandwidth.marks}}"
                        max="{{bandwidth.max}}"
                        min="{{bandwidth.min}}"
                        value="{=formData.bandwidth=}"
                    />
                    <div class="dragger-input">
                        <s-input-number
                            value="{=formData.bandwidth=}"
                            max="{{bandwidth.max}}"
                            min="{{bandwidth.min}}"
                            on-input="bandWidthInputChange"
                        />
                        Mbps
                    </div>
                </s-form-item>
                <p class="alert_class" s-html="{{bandwidthTipMessage}}"></p>
                <p class="help_class">{{bandWidthHelp}}</p>
            </div>
        </s-form>
        <div slot="pageFooter" class="buybucket">
            <div class="buybucket-container">
                <s-tooltip placement="top" trigger="{{buybucket.disabledConfirm ? 'hover' : ''}}">
                    <!--bca-disable-next-line-->
                    <span slot="content" s-html="{{disabledTip ||  '请先变更配置'}}"></span>
                    <s-button skin="primary" size="large" on-click="onConfirm" disabled="{{buybucket.disabledConfirm}}"
                        >{{'确定'}}</s-button
                    >
                </s-tooltip>
                <s-button size="large" on-click="cancel">取消</s-button>
            </div>
        </div>
    </s-app-create-page>
</template>`;
/* eslint-enable */

@template(tpl)
@invokeSUIBIZ
@invokeSUI
class endpointUpgrade extends Component {
    static filters = {
        timeFormat(time) {
            return utcToTime(time);
        },
        statusStyle(status) {
            let config = EndpointStatus.fromValue(status);
            return config ? config.styleClass : '';
        },
        statusText(status) {
            let config = EndpointStatus.fromValue(status);
            return config ? config.text : '';
        }
    };
    static computed = {
        'bandwidth.marks'() {
            let marks = {};
            let min = this.data.get('bandwidth.min');
            let max = this.data.get('bandwidth.max');
            let middle = u.round(max / 2);
            marks[min] = min + 'Mbps';
            marks[max] = max + 'Mbps';
            marks[middle] = middle + 'Mbps';
            return marks;
        },
        'bandWidthHelp'() {
            const totalQuota =
                this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.usedBandwidth +
                    this.data.get('formData.bandwidth') -
                    this.data.get('instance.bandwidth') ||
                0 ||
                '-';
            const quota = this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.totalBandwidth || '-';
            return `单地域所有服务网卡的累计购买带宽峰值总和/配额：${totalQuota}Mbps/${quota}Mbps`;
        },
        'bandwidthTipMessage'() {
            const maxBandwidth = this.data.get('bandwidth.max');
            const ticketUrl = this.data.get('ContextService.Domains.ticket');
            return `温馨提示：若需要大于${maxBandwidth}的带宽限额，请提交<a href="${ticketUrl}/#/ticket/create" target="_blank"> 工单</a>申请`;
        }
    };

    initData() {
        return {
            stepIndex: 0,
            pageNav: {
                title: '变更配置',
                backLabel: '返回',
                backUrl: '/network/#/vpc/endpoint/list'
            },
            confirmPageNav: {
                title: '确认订单',
                backLabel: '返回',
                backUrl: '/network/#/vpc/endpoint/list'
            },
            formData: {
                productType: PayType.POSTPAY,
                subProductType: ''
            },
            ContextService,
            buybucket: {
                confirmText: '确认变更',
                datasource: [{label: '购买配置', value: '带宽0Mbps'}],
                disabledConfirm: true
            },
            network: {
                min: 1,
                max: 2000
            },
            bucketItems: [],
            bucketPrice: [],
            bandwidth: {
                max: 1000,
                min: 1,
                value: 1
            },
            FLAG,
            urlQuery: getQueryParams()
        };
    }
    async inited() {
        await this.getQuota();
        this.loadDetail().then(() => {
            this.loadVpcDetail();
        });
        this.watch('formData.bandwidth', bandwidth => {
            let network = this.data.get('bandwidth');
            const totalQuota =
                (this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.usedBandwidth || 0) +
                bandwidth -
                (this.data.get('instance.bandwidth') || 0);
            const quota = this.data.get('instanceQuota')?.regionTotalBandwidthQuota?.totalBandwidth || 4000;
            if (
                this.data.get('instance') &&
                this.data.get('instance').bandwidth !== bandwidth &&
                bandwidth >= network.min &&
                bandwidth <= network.max
            ) {
                if (totalQuota > quota) {
                    this.data.set('buybucket.disabledConfirm', true);
                    this.data.set(
                        'disabledTip',
                        `当前地域下带宽总配额不足，请提交<a href="${ContextService.Domains.ticket}/#/ticket/create" target="_blank">工单</a>`
                    );
                } else {
                    this.data.set('buybucket.disabledConfirm', false);
                    this.data.set('disabledTip', '');
                }
            } else {
                this.data.set('buybucket.disabledConfirm', true);
                this.data.set('disabledTip', '');
            }
        });
    }

    loadVpcDetail() {
        const vpcId = this.data.get('urlQuery.vpcId');
        return this.$http
            .vpcInfo({
                vpcIds: [vpcId]
            })
            .then(data => {
                const result = data[vpcId] || {};
                this.data.set('vpcInfo', result);
            });
    }

    loadDetail() {
        return this.$http
            .getEndpointDetail(
                {
                    endpointId: this.data.get('urlQuery.shortId')
                },
                kXhrOptions.customSilent
            )
            .then(data => {
                let config = EndpointStatus.fromValue(data.status);
                data.styleClass = config.styleClass;
                data.statusText = config.text;
                data.showRegion = window.$context.getCurrentRegion().label;
                this.data.set('instance', data);
                let bandMiddle = 500;
                bandMiddle = Math.floor((1 + this.data.get('instanceQuota')?.perSnicBandwidthMaxSize || 1000) / 2);
                this.data.set('formData.bandwidth', data.bandwidth || bandMiddle);
            });
    }

    cancel() {
        location.hash = '#/vpc/endpoint/list';
    }

    onConfirm() {
        this.data.set('buybucket.disabledConfirm', true);
        this.$http
            .updateEndpoint({
                endpointId: this.data.get('instance.shortId'),
                bandwidth: this.data.get('formData.bandwidth')
            })
            .then(() => {
                this.data.set('buybucket.disabledConfirm', false);
                location.hash = '#/vpc/endpoint/list';
            })
            .catch(() => this.data.set('buybucket.disabledConfirm', false));
    }

    onRegionChange() {
        location.hash = '#/vpc/endpoint/list';
    }

    getQuota() {
        return this.$http.enicBandWidthQuota().then(res => {
            this.data.set('instanceQuota', res);
            this.data.set('bandwidth.max', res?.perSnicBandwidthMaxSize || 1000);
        });
    }
}

export default San2React(Processor.autowireUnCheckCmpt(endpointUpgrade));
